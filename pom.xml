<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.2.1</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.example</groupId>
	<artifactId>demo</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>war</packaging>
	<name>demo</name>
	<description>Complete PHIVE Validation API with XML, XSD, and Schematron validation like Peppol e-invoice</description>
	<properties>
		<java.version>17</java.version>
		<phive.version>9.2.1</phive.version>
		<ph-commons.version>11.1.5</ph-commons.version>
		<ph-schematron.version>8.0.0</ph-schematron.version>
		<langchain4j.version>0.25.0</langchain4j.version>
		<saxon.version>12.4</saxon.version>
		<lombok.version>1.18.30</lombok.version>
	</properties>

	<repositories>
		<repository>
			<id>helger-maven</id>
			<url>https://repo.helger.com/maven2/</url>
		</repository>
	</repositories>

	<dependencies>
		<!-- Spring Boot Starters -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
			<scope>provided</scope>
		</dependency>

		<!-- PHIVE Validation Libraries - Complete Suite -->
		<dependency>
			<groupId>com.helger.phive</groupId>
			<artifactId>phive-api</artifactId>
			<version>${phive.version}</version>
		</dependency>
		<dependency>
			<groupId>com.helger.phive</groupId>
			<artifactId>phive-xml</artifactId>
			<version>${phive.version}</version>
		</dependency>

		<dependency>
			<groupId>com.helger</groupId>
			<artifactId>ph-commons</artifactId>
			<version>9.4.4</version>
		</dependency>

		<dependency>
			<groupId>com.helger</groupId>
			<artifactId>ph-xml</artifactId>
			<version>9.4.4</version>
		</dependency>

		<dependency>
			<groupId>com.helger.diver</groupId>
			<artifactId>ph-diver-api</artifactId>
			<version>3.0.1</version> <!-- Check for latest version -->
		</dependency>

		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-simple</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.helger.phive</groupId>
			<artifactId>phive-result</artifactId>
			<version>${phive.version}</version>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>com.helger.phive</groupId>-->
<!--			<artifactId>phive-engine</artifactId>-->
<!--		</dependency>-->

		<!-- Ph-Commons Libraries - XML Processing -->
		<dependency>
			<groupId>com.helger.commons</groupId>
			<artifactId>ph-commons</artifactId>
			<version>${ph-commons.version}</version>
		</dependency>
		<dependency>
			<groupId>com.helger.commons</groupId>
			<artifactId>ph-xml</artifactId>
			<version>${ph-commons.version}</version>
		</dependency>
		<dependency>
			<groupId>com.helger.commons</groupId>
			<artifactId>ph-jaxb</artifactId>
			<version>${ph-commons.version}</version>
		</dependency>

		<!-- Schematron Support - Complete Business Rules Validation -->
		<dependency>
			<groupId>com.helger.schematron</groupId>
			<artifactId>ph-schematron-api</artifactId>
			<version>${ph-schematron.version}</version>
		</dependency>
		<dependency>
			<groupId>com.helger.schematron</groupId>
			<artifactId>ph-schematron-xslt</artifactId>
			<version>${ph-schematron.version}</version>
		</dependency>
		<dependency>
			<groupId>com.helger.schematron</groupId>
			<artifactId>ph-schematron-pure</artifactId>
			<version>${ph-schematron.version}</version>
		</dependency>
		<dependency>
			<groupId>com.helger.schematron</groupId>
			<artifactId>ph-schematron-schxslt</artifactId>
			<version>${ph-schematron.version}</version>
		</dependency>

		<!-- Saxon for XSLT Processing -->
		<dependency>
			<groupId>net.sf.saxon</groupId>
			<artifactId>Saxon-HE</artifactId>
			<version>${saxon.version}</version>
		</dependency>

		<!-- UBL and Peppol Support -->
		<dependency>
			<groupId>com.helger.ubl</groupId>
			<artifactId>ph-ubl21</artifactId>
			<version>8.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.helger.ubl</groupId>
			<artifactId>ph-ubl21-codelists</artifactId>
			<version>8.0.0</version>
		</dependency>

		<!-- Lombok for reducing boilerplate code -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- LangChain4j for AI features -->
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-open-ai</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>
		<dependency>
			<groupId>dev.langchain4j</groupId>
			<artifactId>langchain4j-embeddings-all-minilm-l6-v2</artifactId>
			<version>${langchain4j.version}</version>
		</dependency>

		<!-- Test Dependencies -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>de.flapdoodle.embed</groupId>
			<artifactId>de.flapdoodle.embed.mongo</artifactId>
			<version>4.9.2</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.11.0</version>
				<configuration>
					<source>17</source>
					<target>17</target>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
							<version>${lombok.version}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
