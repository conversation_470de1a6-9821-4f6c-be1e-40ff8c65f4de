# PHIVE Validation API

A Spring Boot REST API for XML validation using the PHIVE (Philip <PERSON>r Integrative Validation Engine) libraries. This API provides comprehensive XML validation capabilities including XSD schema validation and Schematron business rule validation.

## Features

- **XSD Schema Validation**: Validate XML documents against XML Schema definitions
- **Schematron Business Rules**: Apply business logic validation using Schematron rules
- **Multiple Validation Layers**: Support for validation pyramids (XSD + multiple Schematron layers)
- **RESTful API**: Clean REST endpoints for validation operations
- **Detailed Error Reporting**: Comprehensive error messages with line/column information
- **Multiple Document Types**: Support for various document types (UBL, Peppol, custom schemas)
- **Configurable Validation Sets**: Easy registration of new validation rule sets

## Quick Start

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Build and Run

```bash
# Clone the repository
git clone <repository-url>
cd phive-validation-api

# Build the application
mvn clean package

# Run the application
mvn spring-boot:run

# Or run the JAR file
java -jar target/phive-validation-api-1.0.0.jar
```

The API will be available at `http://localhost:8080`

## API Endpoints

### Health Check
```
GET /api/v1/validation/health
```
Returns service health status and available validation sets.

### Get Available Validation Sets
```
GET /api/v1/validation/executor-sets
```
Returns list of all registered validation executor sets.

### Main Validation Endpoint
```
POST /api/v1/validation/validate
Content-Type: application/json

{
  "xmlContent": "<?xml version=\"1.0\"?>...",
  "validationExecutorSetId": "com.example:simple:1.0",
  "locale": "en",
  "includeSuccessDetails": false,
  "stopOnFirstError": false
}
```

### Simple Validation Endpoint
```
POST /api/v1/validation/validate-simple?vesId=com.example:simple:1.0&locale=en
Content-Type: text/xml

<?xml version="1.0" encoding="UTF-8"?>
<root xmlns="urn:example:simple">
  <name>Test Document</name>
  <value>42</value>
</root>
```

### Examples and Documentation
```
GET /api/v1/validation/examples
```
Returns API usage examples and documentation.

## Validation Response Format

```json
{
  "success": true,
  "validationExecutorSetId": "com.example:simple:1.0",
  "validationExecutorSetName": "Simple Example Validation",
  "totalDurationMs": 45,
  "timestamp": "2024-01-15T10:30:00",
  "layerResults": [
    {
      "layerIndex": 1,
      "layerName": "XML Schema",
      "validationType": "xsd",
      "artefactPath": "schemas/simple-example.xsd",
      "success": true,
      "skipped": false,
      "durationMs": 25,
      "errors": []
    }
  ],
  "summary": {
    "totalLayers": 1,
    "successfulLayers": 1,
    "failedLayers": 0,
    "skippedLayers": 0,
    "totalErrors": 0,
    "totalWarnings": 0
  }
}
```

## Pre-configured Validation Sets

The API comes with several pre-configured validation sets:

### 1. Simple Example (`com.example:simple:1.0`)
Basic XSD validation for simple XML documents with name/value structure.

**Example XML:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<root xmlns="urn:example:simple">
  <name>Test Document</name>
  <value>42</value>
</root>
```

### 2. Basic XSD (`com.example:basic-xsd:1.0`)
More complex document structure with headers, sections, and attributes.

### 3. UBL Invoice (`org.oasis.ubl:invoice:2.1`)
UBL 2.1 Invoice validation (requires UBL schema files).

## Adding Custom Validation Sets

You can add custom validation sets by modifying the `PhiveConfiguration` class:

```java
private void registerCustomValidation(ValidationExecutorSetRegistry<IValidationSourceXML> registry) {
    // Create XSD validator
    ValidationExecutorXSD xsdValidator = ValidationExecutorXSD.create(
        new ClassPathResource("schemas/my-custom-schema.xsd"));
    
    // Create Schematron validator (optional)
    ValidationExecutorSchematron schematronValidator = ValidationExecutorSchematron.createXSLT(
        new ClassPathResource("schematron/my-business-rules.xslt"));
    
    // Create validation executor set
    ValidationExecutorSet<IValidationSourceXML> customVES = ValidationExecutorSet.create(
        DVRCoordinate.create("com.mycompany", "my-document", "1.0"),
        "My Custom Document Validation",
        ValidationExecutorSetStatus.createValidNow(),
        xsdValidator,
        schematronValidator);
    
    // Register the validation set
    registry.registerValidationExecutorSet(customVES);
}
```

## Configuration

The application can be configured via `application.yml`:

```yaml
phive:
  validation:
    default-locale: en
    max-content-size: 10485760  # 10MB
    detailed-errors: true
    cache-enabled: false
```

## Error Handling

The API provides detailed error information including:

- **Validation Layer**: Which validation layer failed (XSD, Schematron, etc.)
- **Error Level**: ERROR, WARN, INFO, etc.
- **Location Information**: Line and column numbers where possible
- **Detailed Messages**: Human-readable error descriptions
- **Timing Information**: Performance metrics for each validation layer

## Testing

Run the test suite:

```bash
mvn test
```

The tests include:
- API endpoint testing
- Validation logic testing
- Error handling testing
- Performance testing

## Dependencies

Key dependencies used in this project:

- **Spring Boot 3.2.1**: Web framework and dependency injection
- **PHIVE Libraries**: Core validation engine
  - `phive-api`: Core interfaces and abstractions
  - `phive-xml`: XML validation implementation
  - `phive-result`: Result transformation utilities
- **Ph-Commons**: Utility libraries for XML processing
- **Ph-Schematron**: Schematron validation engine

## Architecture

The application follows a layered architecture:

1. **Controller Layer**: REST endpoints and request/response handling
2. **Service Layer**: Business logic and PHIVE integration
3. **Configuration Layer**: Validation set registration and setup
4. **DTO Layer**: Data transfer objects for API communication

## Performance Considerations

- **Schema Caching**: XSD schemas are cached to improve performance
- **Lazy Loading**: Validation resources are loaded on-demand
- **Memory Management**: Large XML documents are processed efficiently
- **Concurrent Processing**: Thread-safe validation execution

## Troubleshooting

### Common Issues

1. **"Validation executor set not found"**
   - Check available validation sets via `/executor-sets` endpoint
   - Ensure the VES ID format is correct: `groupId:artifactId:version`

2. **"Invalid XML content"**
   - Verify XML is well-formed
   - Check character encoding (UTF-8 recommended)

3. **Schema loading errors**
   - Ensure schema files are in the classpath
   - Check file paths in the configuration

### Logging

Enable debug logging for detailed information:

```yaml
logging:
  level:
    com.example.phive: DEBUG
    com.helger.phive: DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.
