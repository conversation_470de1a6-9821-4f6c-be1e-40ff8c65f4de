package com.example.demo.validation;

import com.example.demo.validation.dto.ValidationRequest;
import com.example.demo.validation.dto.ValidationResult;
import com.example.demo.validation.dto.ValidationSetInfo;
import com.example.demo.validation.service.CustomValidationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for Custom PHIVE Validation Service
 */
@SpringBootTest
class CustomValidationServiceTest {

    @Autowired
    private CustomValidationService validationService;

    @Test
    void testValidationServiceInitialization() {
        assertNotNull(validationService);
        
        List<ValidationSetInfo> validationSets = validationService.getAvailableValidationSets();
        assertNotNull(validationSets);
        assertTrue(validationSets.size() > 0);
        
        System.out.println("Available validation sets:");
        validationSets.forEach(set -> 
            System.out.println("- " + set.getId() + ": " + set.getDisplayName()));
    }

    @Test
    void testBasicDocumentValidation() throws Exception {
        String xmlContent = loadTestXml("test-data/sample-basic-document.xml");
        
        ValidationResult result = validationService.validateXml(
            xmlContent, "com.example.validation:basic-xml:1.0");
        
        assertNotNull(result);
        System.out.println("Basic document validation result: " + result.isSuccess());
        System.out.println("Duration: " + result.getDurationMs() + "ms");
        
        if (!result.isSuccess()) {
            System.out.println("Error: " + result.getErrorMessage());
        }
    }

    @Test
    void testInvoiceValidation() throws Exception {
        String xmlContent = loadTestXml("test-data/sample-invoice.xml");
        
        ValidationRequest request = ValidationRequest.builder()
            .xmlContent(xmlContent)
            .validationSetId("com.example.validation:invoice:1.0")
            .includeWarnings(true)
            .includeMetrics(true)
            .requestId("TEST-001")
            .build();
        
        ValidationResult result = validationService.validateDocument(request);
        
        assertNotNull(result);
        System.out.println("Invoice validation result: " + result.isSuccess());
        System.out.println("Duration: " + result.getDurationMs() + "ms");
        
        if (result.getMetrics() != null) {
            System.out.println("Metrics: " + result.getMetrics());
        }
        
        if (!result.isSuccess()) {
            System.out.println("Error: " + result.getErrorMessage());
        }
    }

    @Test
    void testInvalidXmlValidation() {
        String invalidXml = "<InvalidDocument><MissingClosingTag></InvalidDocument>";
        
        ValidationResult result = validationService.validateXml(
            invalidXml, "com.example.validation:basic-xml:1.0");
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNotNull(result.getErrorMessage());
        
        System.out.println("Invalid XML validation (expected failure): " + result.isSuccess());
        System.out.println("Error message: " + result.getErrorMessage());
    }

    @Test
    void testNonExistentValidationSet() {
        String xmlContent = "<test>content</test>";
        
        ValidationResult result = validationService.validateXml(
            xmlContent, "non.existent:validation:1.0");
        
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertTrue(result.getErrorMessage().contains("not found"));
        
        System.out.println("Non-existent validation set (expected failure): " + result.isSuccess());
        System.out.println("Error message: " + result.getErrorMessage());
    }

    @Test
    void testValidationSetInfo() {
        List<ValidationSetInfo> validationSets = validationService.getAvailableValidationSets();
        
        assertNotNull(validationSets);
        assertTrue(validationSets.size() > 0);
        
        ValidationSetInfo firstSet = validationSets.get(0);
        assertNotNull(firstSet.getId());
        assertNotNull(firstSet.getDisplayName());
        assertNotNull(firstSet.getStatus());
        assertTrue(firstSet.getExecutorCount() >= 0);
        
        System.out.println("First validation set info:");
        System.out.println("- ID: " + firstSet.getId());
        System.out.println("- Display Name: " + firstSet.getDisplayName());
        System.out.println("- Status: " + firstSet.getStatus());
        System.out.println("- Executor Count: " + firstSet.getExecutorCount());
    }

    @Test
    void testCacheClear() {
        // Test cache clearing functionality
        validationService.clearValidationCache();
        
        // Validation should still work after cache clear
        List<ValidationSetInfo> validationSets = validationService.getAvailableValidationSets();
        assertNotNull(validationSets);
        assertTrue(validationSets.size() > 0);
        
        System.out.println("Cache cleared successfully, validation sets still available: " + 
                          validationSets.size());
    }

    @Test
    void testValidationWithCustomOptions() throws Exception {
        String xmlContent = loadTestXml("test-data/sample-invoice.xml");
        
        ValidationRequest request = ValidationRequest.builder()
            .xmlContent(xmlContent)
            .validationSetId("com.example.validation:invoice:1.0")
            .includeWarnings(false)
            .includeMetrics(true)
            .stopOnFirstError(true)
            .requestId("TEST-CUSTOM-001")
            .clientId("TEST-CLIENT")
            .documentType("INVOICE")
            .build();
        
        ValidationResult result = validationService.validateDocument(request);
        
        assertNotNull(result);
        System.out.println("Custom options validation result: " + result.isSuccess());
        
        if (result.getValidationLayers() != null) {
            System.out.println("Validation layers: " + result.getValidationLayers().size());
        }
    }

    /**
     * Helper method to load test XML files
     */
    private String loadTestXml(String resourcePath) throws Exception {
        ClassPathResource resource = new ClassPathResource(resourcePath);
        return StreamUtils.copyToString(resource.getInputStream(), StandardCharsets.UTF_8);
    }
}
