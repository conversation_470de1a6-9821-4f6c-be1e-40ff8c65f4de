package com.example.demo.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.Data;

@Data
@Document(collection = "patients")
public class Patient {
    @Id
    private String id;
    private String name;
    private PatientRecord record;
    
    public Patient() {}
    
    public Patient(String name, PatientRecord record) {
        this.name = name;
        this.record = record;
    }
}