package com.example.demo.validation.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * Validation request DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationRequest {
    
    @NotBlank(message = "XML content is required")
    private String xmlContent;
    
    @NotBlank(message = "Validation set ID is required")
    private String validationSetId;
    
    private boolean includeWarnings = true;
    private boolean includeMetrics = true;
    private boolean stopOnFirstError = false;
    
    // Additional validation options
    private Map<String, Object> validationOptions;
    
    // Custom namespace mappings for Schematron validation
    private Map<String, String> namespaceMappings;
    
    // Request metadata
    private String requestId;
    private String clientId;
    private String documentType;
}
