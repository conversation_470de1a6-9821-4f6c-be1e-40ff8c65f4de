package com.example.demo.validation.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Information about available validation sets
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationSetInfo {
    
    private String id;
    private String displayName;
    private String status;
    private int executorCount;
    private String description;
    private String version;
    private List<String> supportedDocumentTypes;
    private List<ValidationLayerInfo> layers;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationLayerInfo {
        private String layerName;
        private String layerType; // XSD, SCHEMATRON
        private String description;
        private boolean required;
        private int order;
    }
}
