package com.example.demo.validation.dto;

import com.helger.phive.api.result.ValidationResultList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Validation result DTO for API responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResult {
    
    private boolean success;
    private String validationSetId;
    private String errorMessage;
    private long durationMs;
    private LocalDateTime timestamp;
    private ValidationResultList resultList;
    private List<ValidationLayer> validationLayers;
    private ValidationMetrics metrics;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationLayer {
        private String layerName;
        private String layerType; // XSD, SCHEMATRON
        private boolean success;
        private List<ValidationError> errors;
        private List<ValidationWarning> warnings;
        private long executionTimeMs;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationError {
        private String errorCode;
        private String message;
        private String severity;
        private LocationInfo location;
        private String rule;
        private String context;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationWarning {
        private String warningCode;
        private String message;
        private LocationInfo location;
        private String rule;
        private String context;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfo {
        private Integer lineNumber;
        private Integer columnNumber;
        private String xpath;
        private String elementName;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationMetrics {
        private int totalErrors;
        private int totalWarnings;
        private int layersExecuted;
        private long totalExecutionTimeMs;
        private String validationSetVersion;
    }
}
