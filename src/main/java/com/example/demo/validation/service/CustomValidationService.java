package com.example.demo.validation.service;

import com.example.demo.validation.core.AbstractValidationService;
import com.example.demo.validation.dto.ValidationRequest;
import com.example.demo.validation.dto.ValidationResult;
import com.helger.phive.api.executorset.IValidationExecutorSet;
import com.helger.phive.api.executorset.ValidationExecutorSet;
import com.helger.phive.api.result.ValidationResultList;
import com.helger.phive.xml.schematron.ValidationExecutorSchematron;
import com.helger.phive.xml.source.IValidationSourceXML;
import com.helger.phive.xml.xsd.ValidationExecutorXSD;
import com.helger.xml.namespace.MapBasedNamespaceContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Custom PHIVE validation service implementation
 * 
 * This service demonstrates how to extend the AbstractValidationService
 * to implement custom validation logic with XSD and Schematron rules.
 * 
 * Features:
 * - Custom validation rule registration
 * - Extensible validation logic
 * - Support for multiple document types
 * - Detailed error reporting
 * - Performance metrics
 */
@Service
@Slf4j
public class CustomValidationService extends AbstractValidationService {

    @PostConstruct
    public void initialize() {
        log.info("Initializing Custom Validation Service...");
        registerValidationRules();
        log.info("Custom Validation Service initialized with {} validation sets", 
                getAvailableValidationSets().size());
    }

    /**
     * Register custom validation rules
     * Override this method to add your own validation sets
     */
    @Override
    public void registerValidationRules() {
        log.info("Registering custom validation rules...");

        // 1. Basic XML validation
        registerBasicXMLValidation();

        // 2. Invoice validation with business rules
        registerInvoiceValidation();

        // 3. Purchase Order validation
        registerPurchaseOrderValidation();

        // 4. Custom document validation
        registerCustomDocumentValidation();

        log.info("Completed registration of custom validation rules");
    }

    /**
     * Enhanced validation method with custom processing
     */
    public ValidationResult validateDocument(ValidationRequest request) {
        log.info("Starting document validation for set: {}", request.getValidationSetId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Apply custom namespace mappings if provided
            applyCustomNamespaceMappings(request.getNamespaceMappings());
            
            // Execute base validation
            ValidationResult baseResult = validateXml(request.getXmlContent(), request.getValidationSetId());
            
            // Enhance result with additional processing
            return enhanceValidationResult(baseResult, request, startTime);
            
        } catch (Exception e) {
            log.error("Document validation failed for set: {}", request.getValidationSetId(), e);
            return createErrorResult("Document validation failed: " + e.getMessage(), startTime);
        }
    }

    /**
     * Register basic XML validation (XSD only)
     */
    private void registerBasicXMLValidation() {
        try {
            ValidationExecutorXSD xsdValidator = createXSDValidator("schemas/basic/basic-document.xsd");
            
            if (xsdValidator != null) {
                ValidationExecutorSet<IValidationSourceXML> executorSet = createValidationExecutorSet(
                    "com.example.validation", "basic-xml", "1.0",
                    "Basic XML Schema Validation",
                    xsdValidator
                );
                
                registerValidationExecutorSet(executorSet);
                log.info("Registered basic XML validation set");
            }
        } catch (Exception e) {
            log.warn("Could not register basic XML validation: {}", e.getMessage());
        }
    }

    /**
     * Register invoice validation with XSD + Schematron business rules
     */
    private void registerInvoiceValidation() {
        try {
            // XSD validation
            ValidationExecutorXSD xsdValidator = createXSDValidator("schemas/invoice/invoice-schema.xsd");
            
            // Schematron business rules
            ValidationExecutorSchematron businessRules = createSchematronValidator(
                "schematron/invoice/invoice-business-rules.xslt");
            
            ValidationExecutorSchematron advancedRules = createSchematronValidator(
                "schematron/invoice/invoice-advanced-rules.xslt");
            
            if (xsdValidator != null) {
                ValidationExecutorSet<IValidationSourceXML> executorSet = createValidationExecutorSet(
                    "com.example.validation", "invoice", "1.0",
                    "Invoice Validation (XSD + Business Rules)",
                    xsdValidator, businessRules, advancedRules
                );
                
                registerValidationExecutorSet(executorSet);
                log.info("Registered invoice validation set with {} layers", 
                        (businessRules != null ? 2 : 1) + (advancedRules != null ? 1 : 0));
            }
        } catch (Exception e) {
            log.warn("Could not register invoice validation: {}", e.getMessage());
        }
    }

    /**
     * Register purchase order validation
     */
    private void registerPurchaseOrderValidation() {
        try {
            ValidationExecutorXSD xsdValidator = createXSDValidator("schemas/order/purchase-order-schema.xsd");
            
            // Create namespace context for Schematron
            MapBasedNamespaceContext namespaceContext = new MapBasedNamespaceContext();
            namespaceContext.addMapping("po", "urn:example:purchaseorder");
            namespaceContext.addMapping("common", "urn:example:common");
            
            ValidationExecutorSchematron businessRules = createSchematronValidator(
                "schematron/order/purchase-order-rules.xslt");
                    //, namespaceContext);
            
            if (xsdValidator != null) {
                ValidationExecutorSet<IValidationSourceXML> executorSet = createValidationExecutorSet(
                    "com.example.validation", "purchase-order", "1.0",
                    "Purchase Order Validation",
                    xsdValidator, businessRules
                );
                
                registerValidationExecutorSet(executorSet);
                log.info("Registered purchase order validation set");
            }
        } catch (Exception e) {
            log.warn("Could not register purchase order validation: {}", e.getMessage());
        }
    }

    /**
     * Register custom document validation with dynamic rules
     */
    private void registerCustomDocumentValidation() {
        try {
            ValidationExecutorXSD xsdValidator = createXSDValidator("schemas/custom/custom-document.xsd");
            
            if (xsdValidator != null) {
                ValidationExecutorSet<IValidationSourceXML> executorSet = createValidationExecutorSet(
                    "com.example.validation", "custom-document", "1.0",
                    "Custom Document Validation",
                    xsdValidator
                );
                
                registerValidationExecutorSet(executorSet);
                log.info("Registered custom document validation set");
            }
        } catch (Exception e) {
            log.warn("Could not register custom document validation: {}", e.getMessage());
        }
    }

    /**
     * Override to add custom pre-validation logic
     */
    @Override
    protected void preValidationHook(IValidationExecutorSet<IValidationSourceXML> executorSet, 
                                   IValidationSourceXML validationSource) {
        super.preValidationHook(executorSet, validationSource);
        
        // Add custom pre-validation logic here
        log.debug("Custom pre-validation processing for: {}", executorSet.getID());
        
        // Example: Log validation attempt
        // Example: Apply custom transformations
        // Example: Validate prerequisites
    }

    /**
     * Override to add custom post-validation logic
     */
    @Override
    protected void postValidationHook(IValidationExecutorSet<IValidationSourceXML> executorSet, 
                                    IValidationSourceXML validationSource, 
                                    ValidationResultList resultList) {
        super.postValidationHook(executorSet, validationSource, resultList);
        
        // Add custom post-validation logic here
        log.debug("Custom post-validation processing for: {} - Errors: {}", 
                 executorSet.getID(), resultList.getCount());
        
        // Example: Send notifications
        // Example: Log to audit system
        // Example: Update metrics
    }

    /**
     * Apply custom namespace mappings for Schematron validation
     */
    private void applyCustomNamespaceMappings(Map<String, String> namespaceMappings) {
        if (namespaceMappings != null && !namespaceMappings.isEmpty()) {
            log.debug("Applying custom namespace mappings: {}", namespaceMappings);
            // Implementation would depend on how you want to handle dynamic namespace mappings
        }
    }

    /**
     * Enhance validation result with additional information
     */
    private ValidationResult enhanceValidationResult(ValidationResult baseResult, 
                                                   ValidationRequest request, 
                                                   long startTime) {
        
        // Create enhanced result with additional metadata
        ValidationResult enhancedResult = ValidationResult.builder()
            .success(baseResult.isSuccess())
            .validationSetId(baseResult.getValidationSetId())
            .errorMessage(baseResult.getErrorMessage())
            .durationMs(baseResult.getDurationMs())
            .timestamp(LocalDateTime.now())
            .resultList(baseResult.getResultList())
            .validationLayers(extractValidationLayers(baseResult.getResultList()))
            .metrics(calculateMetrics(baseResult, request))
            .build();
        
        return enhancedResult;
    }

    /**
     * Extract validation layers from result list
     */
    private List<ValidationResult.ValidationLayer> extractValidationLayers(ValidationResultList resultList) {
        List<ValidationResult.ValidationLayer> layers = new ArrayList<>();
        
        if (resultList != null) {
            for (int i = 0; i < resultList.size(); i++) {
                com.helger.phive.api.result.ValidationResult result = resultList.get(i);
                
                ValidationResult.ValidationLayer layer = ValidationResult.ValidationLayer.builder()
                    .layerName("Layer " + (i + 1))
                    .layerType(determineLayerType(result))
                    .success(result.isSuccess())
                    .errors(convertErrors(result))
                    .warnings(convertWarnings(result))
                    .executionTimeMs(0L) // Would need to track this separately
                    .build();
                
                layers.add(layer);
            }
        }
        
        return layers;
    }

    /**
     * Determine layer type from validation result
     */
    private String determineLayerType(com.helger.phive.api.result.ValidationResult result) {
        // This is a simplified implementation
        // You might want to track this information during validation execution
        return result.getErrorList().isEmpty() ? "XSD" : "SCHEMATRON";
    }

    /**
     * Convert PHIVE errors to our DTO format
     */
    private List<ValidationResult.ValidationError> convertErrors(com.helger.phive.api.result.ValidationResult result) {
        List<ValidationResult.ValidationError> errors = new ArrayList<>();
        
        result.getErrorList().forEach(error -> {
            ValidationResult.ValidationError validationError = ValidationResult.ValidationError.builder()
                .errorCode(error.getErrorID())
                .message(error.getErrorText(Locale.ENGLISH))
                .severity("ERROR")
                .location(ValidationResult.LocationInfo.builder()
                    .lineNumber(error.getErrorLocation() != null ? 
                        error.getErrorLocation().getLineNumber() : null)
                    .columnNumber(error.getErrorLocation() != null ? 
                        error.getErrorLocation().getColumnNumber() : null)
                    .build())
                .build();
            
            errors.add(validationError);
        });
        
        return errors;
    }

    /**
     * Convert PHIVE warnings to our DTO format
     */
    private List<ValidationResult.ValidationWarning> convertWarnings(com.helger.phive.api.result.ValidationResult result) {
        // Similar implementation to convertErrors but for warnings
        return new ArrayList<>(); // Simplified for now
    }

    /**
     * Calculate validation metrics
     */
    private ValidationResult.ValidationMetrics calculateMetrics(ValidationResult baseResult, ValidationRequest request) {
        return ValidationResult.ValidationMetrics.builder()
            .totalErrors(baseResult.getResultList() != null ? baseResult.getResultList().getCount() : 0)
            .totalWarnings(baseResult.getResultList() != null ? baseResult.getResultList().getCount() : 0)
            .layersExecuted(baseResult.getResultList() != null ? baseResult.getResultList().size() : 0)
            .totalExecutionTimeMs(baseResult.getDurationMs())
            .validationSetVersion("1.0")
            .build();
    }

    /**
     * Override dynamic validation set creation for custom logic
     */
    @Override
    protected IValidationExecutorSet<IValidationSourceXML> createDynamicValidationSet(String validationSetId) {
        log.info("Creating dynamic validation set for: {}", validationSetId);
        
        // Example: Create validation set based on ID pattern
        if (validationSetId.startsWith("dynamic-")) {
            return createDynamicValidationSetFromId(validationSetId);
        }
        
        return super.createDynamicValidationSet(validationSetId);
    }

    /**
     * Create dynamic validation set from ID
     */
    private IValidationExecutorSet<IValidationSourceXML> createDynamicValidationSetFromId(String validationSetId) {
        // Implementation for dynamic validation set creation
        // This could read configuration from database, files, etc.
        log.warn("Dynamic validation set creation not fully implemented for: {}", validationSetId);
        return null;
    }
}
