package com.example.demo.validation.controller;

import com.example.demo.validation.dto.ValidationRequest;
import com.example.demo.validation.dto.ValidationResult;
import com.example.demo.validation.dto.ValidationSetInfo;
import com.example.demo.validation.service.CustomValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for Custom PHIVE Validation
 * 
 * This controller provides comprehensive XML validation endpoints using PHIVE
 * with support for XSD schema validation and Schematron business rules.
 * 
 * Features:
 * - Multi-layer validation (XSD + Schematron)
 * - Detailed error reporting
 * - Performance metrics
 * - Extensible validation rules
 * - Support for custom document types
 */
@RestController
@RequestMapping("/api/v1/custom-validation")
@RequiredArgsConstructor
@Validated
@Slf4j
public class CustomValidationController {

    private final CustomValidationService validationService;

    /**
     * Main validation endpoint
     * 
     * Validates XML documents using specified validation sets with comprehensive
     * business rules including XSD schema validation and Schematron rules.
     * 
     * @param request Validation request containing XML content and validation set ID
     * @return Detailed validation results with errors, warnings, and metrics
     */
    @PostMapping("/validate")
    public ResponseEntity<ValidationResult> validateDocument(@Valid @RequestBody ValidationRequest request) {
        log.info("Received validation request for set: {} (requestId: {})", 
                request.getValidationSetId(), request.getRequestId());
        
        try {
            ValidationResult result = validationService.validateDocument(request);
            
            // Return appropriate HTTP status based on validation result
            HttpStatus status = result.isSuccess() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;
            
            log.info("Validation completed for set: {} - Success: {} - Duration: {}ms", 
                    request.getValidationSetId(), result.isSuccess(), result.getDurationMs());
            
            return ResponseEntity.status(status).body(result);
            
        } catch (Exception e) {
            log.error("Validation failed for set: {}", request.getValidationSetId(), e);
            
            ValidationResult errorResult = ValidationResult.builder()
                .success(false)
                .validationSetId(request.getValidationSetId())
                .errorMessage("Validation failed: " + e.getMessage())
                .timestamp(LocalDateTime.now())
                .durationMs(0L)
                .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * Simple XML validation endpoint (legacy compatibility)
     * 
     * @param xmlContent XML content to validate
     * @param validationSetId Validation set identifier
     * @return Basic validation result
     */
    @PostMapping("/validate-simple")
    public ResponseEntity<ValidationResult> validateXmlSimple(
            @RequestParam String xmlContent,
            @RequestParam String validationSetId) {
        
        log.info("Received simple validation request for set: {}", validationSetId);
        
        try {
            ValidationResult result = validationService.validateXml(xmlContent, validationSetId);
            
            HttpStatus status = result.isSuccess() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;
            return ResponseEntity.status(status).body(result);
            
        } catch (Exception e) {
            log.error("Simple validation failed for set: {}", validationSetId, e);
            
            ValidationResult errorResult = ValidationResult.builder()
                .success(false)
                .validationSetId(validationSetId)
                .errorMessage("Validation failed: " + e.getMessage())
                .timestamp(LocalDateTime.now())
                .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * Get all available validation sets
     * 
     * @return List of available validation executor sets with metadata
     */
    @GetMapping("/validation-sets")
    public ResponseEntity<List<ValidationSetInfo>> getAvailableValidationSets() {
        log.info("Retrieving available validation sets");
        
        try {
            List<ValidationSetInfo> validationSets = validationService.getAvailableValidationSets();
            
            log.info("Retrieved {} validation sets", validationSets.size());
            return ResponseEntity.ok(validationSets);
            
        } catch (Exception e) {
            log.error("Error retrieving validation sets", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get specific validation set information
     * 
     * @param validationSetId Validation set identifier
     * @return Detailed information about the validation set
     */
    @GetMapping("/validation-sets/{validationSetId}")
    public ResponseEntity<ValidationSetInfo> getValidationSetInfo(@PathVariable String validationSetId) {
        log.info("Retrieving validation set info for: {}", validationSetId);
        
        try {
            List<ValidationSetInfo> allSets = validationService.getAvailableValidationSets();
            
            ValidationSetInfo requestedSet = allSets.stream()
                .filter(set -> set.getId().equals(validationSetId))
                .findFirst()
                .orElse(null);
            
            if (requestedSet != null) {
                return ResponseEntity.ok(requestedSet);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error retrieving validation set info for: {}", validationSetId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Validate multiple documents in batch
     * 
     * @param requests List of validation requests
     * @return List of validation results
     */
    @PostMapping("/validate-batch")
    public ResponseEntity<List<ValidationResult>> validateBatch(@Valid @RequestBody List<ValidationRequest> requests) {
        log.info("Received batch validation request with {} documents", requests.size());
        
        try {
            List<ValidationResult> results = requests.stream()
                .map(validationService::validateDocument)
                .toList();
            
            long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
            
            log.info("Batch validation completed - Success: {}/{}", successCount, results.size());
            
            return ResponseEntity.ok(results);
            
        } catch (Exception e) {
            log.error("Batch validation failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint for validation service
     * 
     * @return Service health status and metrics
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        log.debug("Health check requested");
        
        try {
            List<ValidationSetInfo> validationSets = validationService.getAvailableValidationSets();
            
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            health.put("service", "Custom PHIVE Validation API");
            health.put("version", "1.0.0");
            health.put("availableValidationSets", validationSets.size());
            health.put("validationSets", validationSets.stream()
                .map(ValidationSetInfo::getId)
                .toList());
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("Health check failed", e);
            
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("timestamp", LocalDateTime.now());
            health.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(health);
        }
    }

    /**
     * Clear validation cache (for testing/maintenance)
     * 
     * @return Operation result
     */
    @PostMapping("/admin/clear-cache")
    public ResponseEntity<Map<String, Object>> clearValidationCache() {
        log.info("Clearing validation cache");
        
        try {
            validationService.clearValidationCache();
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Validation cache cleared");
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to clear validation cache", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to clear cache: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Reload validation rules (for dynamic updates)
     * 
     * @return Operation result
     */
    @PostMapping("/admin/reload-rules")
    public ResponseEntity<Map<String, Object>> reloadValidationRules() {
        log.info("Reloading validation rules");
        
        try {
            validationService.clearValidationCache();
            validationService.registerValidationRules();
            
            List<ValidationSetInfo> validationSets = validationService.getAvailableValidationSets();
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Validation rules reloaded");
            response.put("timestamp", LocalDateTime.now());
            response.put("validationSetsCount", validationSets.size());
            response.put("validationSets", validationSets.stream()
                .map(ValidationSetInfo::getId)
                .toList());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to reload validation rules", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to reload rules: " + e.getMessage());
            response.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get validation statistics
     * 
     * @return Validation usage statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getValidationStats() {
        log.debug("Retrieving validation statistics");
        
        try {
            List<ValidationSetInfo> validationSets = validationService.getAvailableValidationSets();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalValidationSets", validationSets.size());
            stats.put("activeValidationSets", validationSets.stream()
                .mapToLong(set -> "ACTIVE".equals(set.getStatus()) ? 1 : 0)
                .sum());
            stats.put("timestamp", LocalDateTime.now());
            
            // Add more statistics as needed
            stats.put("validationSetDetails", validationSets);
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("Failed to retrieve validation statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
