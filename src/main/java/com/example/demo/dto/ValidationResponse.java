package com.example.demo.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response object for XML validation
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResponse {
    
    /**
     * Whether the overall validation was successful
     */
    private boolean success;
    
    /**
     * The validation executor set ID that was used
     */
    private String validationExecutorSetId;
    
    /**
     * The display name of the validation executor set
     */
    private String validationExecutorSetName;
    
    /**
     * Total duration of validation in milliseconds
     */
    private long totalDurationMs;
    
    /**
     * Timestamp when validation was completed
     */
    private LocalDateTime timestamp;
    
    /**
     * Error message if validation failed at a high level
     */
    private String errorMessage;
    
    /**
     * Results for each validation layer
     */
    private List<LayerResult> layerResults;
    
    /**
     * Summary of validation results
     */
    private ValidationSummary summary;
    
    /**
     * Result for a single validation layer
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LayerResult {
        private int layerIndex;
        private String layerName;
        private String validationType; // "xsd", "schematron", etc.
        private String artefactPath;
        private boolean success;
        private boolean skipped;
        private long durationMs;
        private List<ValidationError> errors;
        private List<ValidationWarning> warnings;
    }
    
    /**
     * A validation error
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationError {
        private String errorCode;
        private String message;
        private String severity;
        private LocationInfo location;
        private String ruleId;
        private String test;
    }
    
    /**
     * A validation warning
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationWarning {
        private String warningCode;
        private String message;
        private LocationInfo location;
        private String ruleId;
    }
    
    /**
     * Location information for errors/warnings
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfo {
        private Integer lineNumber;
        private Integer columnNumber;
        private String xpath;
        private String elementName;
    }
    
    /**
     * Summary of validation results
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationSummary {
        private int totalLayers;
        private int successfulLayers;
        private int failedLayers;
        private int skippedLayers;
        private int totalErrors;
        private int totalWarnings;
        private String resultDescription;
    }
}
