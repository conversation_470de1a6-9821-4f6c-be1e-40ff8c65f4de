package com.example.demo.config;

import com.example.demo.model.Patient;
import com.example.demo.model.PatientBilling;
import com.example.demo.model.PatientRecord;
import com.example.demo.repository.PatientRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;

@Configuration
public class MongoDBInitializer {

    private static final Logger logger = LoggerFactory.getLogger(MongoDBInitializer.class);
    
    @Value("${app.db.initialize:true}")
    private boolean shouldInitialize;

    @Bean
    CommandLineRunner commandLineRunner(PatientRepository repository, MongoTemplate mongoTemplate) {
        return args -> {
            try {
                // Simplified initialization without index creation
                if (shouldInitialize) {
                    // Clear existing data
                    repository.deleteAll();
                    
                    // Add some sample data
                    PatientBilling billing1 = new PatientBilling("VISA", "****************");
                    PatientRecord record1 = new PatientRecord("123-45-6789", billing1);
                    Patient patient1 = new Patient("John Doe", record1);
                    
                    PatientBilling billing2 = new PatientBilling("MASTERCARD", "****************");
                    PatientRecord record2 = new PatientRecord("***********", billing2);
                    Patient patient2 = new Patient("Jane Smith", record2);
                    
                    // Save to database
                    repository.save(patient1);
                    repository.save(patient2);
                    
                    logger.info("Database initialized with sample data");
                    logger.info("Total patients: {}", repository.count());
                } else {
                    logger.info("Database initialization skipped (app.db.initialize=false)");
                }
            } catch (Exception e) {
                logger.error("Error during database initialization", e);
            }
        };
    }
}
