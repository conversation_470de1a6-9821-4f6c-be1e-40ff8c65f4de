package com.example.demo.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;


@Configuration
public class MongoDBConfig {

    @Value("${spring.data.mongodb.uri}")
    private String connectionString;

    @PostConstruct
    public void init() {
        try (MongoClient mongoClient = MongoClients.create(connectionString)) {
            // Extract database name from connection string more safely
            String dbName;
            if (connectionString.contains("/")) {
                dbName = connectionString.substring(connectionString.lastIndexOf("/") + 1);
                // Remove query parameters if present
                if (dbName.contains("?")) {
                    dbName = dbName.substring(0, dbName.indexOf("?"));
                }
            } else {
                dbName = "patientdb"; // Default database name
            }
            
            // Get or create database
            MongoDatabase database = mongoClient.getDatabase(dbName);
            
            // Create collections if they don't exist
            if (!database.listCollectionNames().into(new java.util.ArrayList<>()).contains("patients")) {
                database.createCollection("patients");
                System.out.println("Created 'patients' collection");
            }
        }
    }

    @Bean
    public MongoTemplate mongoTemplate() {
        MongoClient mongoClient = MongoClients.create(connectionString);
        
        // Extract database name safely
        String dbName;
        if (connectionString.contains("/")) {
            dbName = connectionString.substring(connectionString.lastIndexOf("/") + 1);
            // Remove query parameters if present
            if (dbName.contains("?")) {
                dbName = dbName.substring(0, dbName.indexOf("?"));
            }
        } else {
            dbName = "patientdb"; // Default database name
        }
        
        return new MongoTemplate(mongoClient, dbName);
    }
}
