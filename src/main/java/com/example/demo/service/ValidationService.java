package com.example.demo.service;

import com.example.demo.dto.ValidationRequest;
import com.example.demo.dto.ValidationResponse;
import com.helger.commons.io.streamprovider.StringInputStreamProvider;
import com.helger.phive.api.execute.ValidationExecutionManager;
import com.helger.phive.api.executorset.IValidationExecutorSet;
import com.helger.phive.api.executorset.ValidationExecutorSetRegistry;
import com.helger.phive.api.result.ValidationResult;
import com.helger.phive.api.result.ValidationResultList;
import com.helger.phive.xml.source.IValidationSourceXML;
import com.helger.phive.xml.source.ValidationSourceXML;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/**
 * Service for XML validation using PHIVE
 * 
 * This service provides comprehensive XML validation capabilities including:
 * - XSD schema validation
 * - Schematron business rules validation
 * - Multi-layer validation with detailed error reporting
 * - Support for various document types (e-invoices, credit notes, etc.)
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ValidationService {

    private final ValidationExecutorSetRegistry<IValidationSourceXML> validationRegistry;

    /**
     * Validate XML content using the specified validation executor set
     */
    public ValidationResponse validateXml(ValidationRequest request) {
        log.info("Starting validation for executor set: {}", request.getValidationExecutorSetId());
        
        long startTime = System.currentTimeMillis();
        
        try {
            // Find the validation executor set
            IValidationExecutorSet<IValidationSourceXML> executorSet = 
                validationRegistry.getOfID(request.getValidationSetId());
            
            if (executorSet == null) {
                return ValidationResponse.builder()
                    .success(false)
                    .validationExecutorSetId(request.getValidationExecutorSetId())
                    .errorMessage("Validation executor set not found: " + request.getValidationExecutorSetId())
                    .timestamp(LocalDateTime.now())
                    .totalDurationMs(System.currentTimeMillis() - startTime)
                    .build();
            }

            // Create validation source from XML string
            IValidationSourceXML validationSource = ValidationSourceXML.create(
                new StringInputStreamProvider(request.getXmlContent(), StandardCharsets.UTF_8));

            // Execute validation
            ValidationResultList resultList = ValidationExecutionManager.executeValidation(
                executorSet, validationSource);

            // Convert results to response
            ValidationResponse response = convertToValidationResponse(
                resultList, 
                request.getValidationExecutorSetId(),
                executorSet.getDisplayName(),
                startTime);

            log.info("Validation completed for executor set: {} - Success: {}", 
                request.getValidationExecutorSetId(), response.isSuccess());

            return response;

        } catch (Exception e) {
            log.error("Error during validation", e);
            return ValidationResponse.builder()
                .success(false)
                .validationExecutorSetId(request.getValidationExecutorSetId())
                .errorMessage("Validation failed: " + e.getMessage())
                .timestamp(LocalDateTime.now())
                .totalDurationMs(System.currentTimeMillis() - startTime)
                .build();
        }
    }

    /**
     * Get all available validation executor sets
     */
    public List<ValidationExecutorSetInfo> getAvailableValidationSets() {
        List<ValidationExecutorSetInfo> result = new ArrayList<>();
        
        for (IValidationExecutorSet<IValidationSourceXML> executorSet : validationRegistry.getAll()) {
            result.add(ValidationExecutorSetInfo.builder()
                .id(String.valueOf(executorSet.getID()))
                .displayName(executorSet.getDisplayName())
                .status(executorSet.getStatus().toString())
                .layerCount(executorSet.getCount())
                .build());
        }
        
        return result;
    }

    /**
     * Check if a validation executor set exists
     */
    public boolean isValidationSetAvailable(String validationExecutorSetId) {
        return validationRegistry.getOfID(validationExecutorSetId) != null;
    }

    /**
     * Get validation executor set information
     */
    public ValidationExecutorSetInfo getValidationSetInfo(String validationExecutorSetId) {
        IValidationExecutorSet<IValidationSourceXML> executorSet = 
            validationRegistry.getOfID(validationExecutorSetId);
        
        if (executorSet == null) {
            return null;
        }
        
        return ValidationExecutorSetInfo.builder()
            .id(String.valueOf(executorSet.getID()))
            .displayName(executorSet.getDisplayName())
            .status(executorSet.getStatus().toString())
            .layerCount(executorSet.getCount())
            .build();
    }

    /**
     * Convert PHIVE ValidationResultList to our ValidationResponse
     */
    private ValidationResponse convertToValidationResponse(
            ValidationResultList resultList, 
            String executorSetId, 
            String executorSetName,
            long startTime) {
        
        long totalDuration = System.currentTimeMillis() - startTime;
        boolean overallSuccess = resultList.containsNoError();
        
        List<ValidationResponse.LayerResult> layerResults = new ArrayList<>();
        int totalErrors = 0;
        int totalWarnings = 0;
        int successfulLayers = 0;
        int failedLayers = 0;
        
        // Process each validation result (layer)
        for (int i = 0; i < resultList.size(); i++) {
            ValidationResult result = resultList.get(i);
            
            List<ValidationResponse.ValidationError> errors = new ArrayList<>();
            List<ValidationResponse.ValidationWarning> warnings = new ArrayList<>();
            
            // Convert errors and warnings
            result.getErrorList().forEach(error -> {
                errors.add(ValidationResponse.ValidationError.builder()
                    .errorCode(error.getErrorID())
                    .message(error.getErrorText(Locale.ENGLISH))
                    .severity("ERROR")
                    .location(ValidationResponse.LocationInfo.builder()
                        .lineNumber(error.getErrorLocation() != null ? 
                            error.getErrorLocation().getLineNumber() : null)
                        .columnNumber(error.getErrorLocation() != null ? 
                            error.getErrorLocation().getColumnNumber() : null)
                        .build())
                    .build());
            });
            
            // For this implementation, we'll treat all issues as errors
            // In a more sophisticated setup, you could differentiate warnings
            
            boolean layerSuccess = errors.isEmpty();
            if (layerSuccess) {
                successfulLayers++;
            } else {
                failedLayers++;
            }
            
            totalErrors += errors.size();
            totalWarnings += warnings.size();
            
            layerResults.add(ValidationResponse.LayerResult.builder()
                .layerIndex(i + 1)
                .layerName("Validation Layer " + (i + 1))
                .validationType(determineValidationType(result))
                .artefactPath(result.getValidationArtefact() != null ? 
                    result.getValidationArtefact().toString() : "Unknown")
                .success(layerSuccess)
                .skipped(false)
                .durationMs(0) // PHIVE doesn't provide per-layer timing
                .errors(errors)
                .warnings(warnings)
                .build());
        }
        
        // Create summary
        ValidationResponse.ValidationSummary summary = ValidationResponse.ValidationSummary.builder()
            .totalLayers(layerResults.size())
            .successfulLayers(successfulLayers)
            .failedLayers(failedLayers)
            .skippedLayers(0)
            .totalErrors(totalErrors)
            .totalWarnings(totalWarnings)
            .resultDescription(overallSuccess ? "Validation successful" : "Validation failed")
            .build();
        
        return ValidationResponse.builder()
            .success(overallSuccess)
            .validationExecutorSetId(executorSetId)
            .validationExecutorSetName(executorSetName)
            .totalDurationMs(totalDuration)
            .timestamp(LocalDateTime.now())
            .layerResults(layerResults)
            .summary(summary)
            .build();
    }
    
    /**
     * Determine validation type from result
     */
    private String determineValidationType(ValidationResult result) {
        if (result.getValidationArtefact() != null) {
            String artefact = result.getValidationArtefact().toString().toLowerCase();
            if (artefact.contains(".xsd")) {
                return "xsd";
            } else if (artefact.contains(".xslt") || artefact.contains("schematron")) {
                return "schematron";
            }
        }
        return "unknown";
    }

    /**
     * Information about a validation executor set
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ValidationExecutorSetInfo {
        private String id;
        private String displayName;
        private String status;
        private int layerCount;
    }
}
