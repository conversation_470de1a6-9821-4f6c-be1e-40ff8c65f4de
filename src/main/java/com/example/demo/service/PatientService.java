package com.example.demo.service;

import com.example.demo.model.Patient;
import com.example.demo.repository.PatientRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import java.util.Map;
import org.springframework.data.mongodb.core.aggregation.AddFieldsOperation;
import org.springframework.data.mongodb.core.aggregation.StringOperators;
import org.springframework.data.domain.Sort;

@Service
public class PatientService {
    private static final Logger logger = LoggerFactory.getLogger(PatientService.class);
    
    private final PatientRepository patientRepository;
    private final MongoTemplate mongoTemplate;
    
    @Autowired
    public PatientService(PatientRepository patientRepository, MongoTemplate mongoTemplate) {
        this.patientRepository = patientRepository;
        this.mongoTemplate = mongoTemplate;
    }
    
    // Create
    public Patient savePatient(Patient patient) {
        logger.debug("Saving patient: {}", patient.getName());
        return patientRepository.save(patient);
    }
    
    // Read all
    public List<Patient> getAllPatients() {
        logger.debug("Getting all patients");
        return patientRepository.findAll();
    }
    
    // Read by id
    public Optional<Patient> getPatientById(String id) {
        logger.debug("Getting patient by ID: {}", id);
        return patientRepository.findById(id);
    }
    
    // Update
    public Patient updatePatient(Patient patient) {
        return patientRepository.save(patient);
    }
    
    // Delete
    public void deletePatient(String id) {
        patientRepository.deleteById(id);
    }

    // Find by card type
    public List<Patient> findPatientsByCardType(String cardType) {
        logger.debug("Finding patients by card type: {}", cardType);
        return patientRepository.findByCardType(cardType);
    }
    public List<Patient> findPatientByName(String name) {
        logger.debug("Finding patients by name: {}", name);
        return patientRepository.findByName(name);
    }

    // Find by card type using aggregation
    public List<Patient> findPatientsByCardTypeAggregation(String cardType) {
        logger.debug("Finding patients by card type using aggregation: {}", cardType);
        return patientRepository.findPatientsByCardTypeAggregation(cardType);
    }

    public List<Patient> findPatientsByCardTypeAdvancedAggregation(String cardType) {
        logger.debug("Finding patients by card type using advanced aggregation: {}", cardType);
        
        MatchOperation matchOperation = Aggregation.match(
            Criteria.where("record.billing.cardType").is(cardType)
        );
        
        ProjectionOperation projectionOperation = Aggregation.project("id", "name")
            .and("record.billing.cardType").as("cardType");
        
        Aggregation aggregation = Aggregation.newAggregation(
            matchOperation,
            projectionOperation
        );
        
        AggregationResults<Patient> results = mongoTemplate.aggregate(
            aggregation, "patients", Patient.class
        );
        
        return results.getMappedResults();
    }

    public List<Map> findPatientsByCardTypeGroupBySSN(String cardType) {
        logger.debug("Finding patients by card type and grouping by SSN: {}", cardType);
        
        // Match operation to filter by card type
        MatchOperation matchOperation = Aggregation.match(
            Criteria.where("record.billing.cardType").is(cardType)
        );
        
        // Group operation to group by SSN
        GroupOperation groupOperation = Aggregation.group("record.ssn")
            .first("record.ssn").as("ssn")
            .count().as("patientCount")
            .push("name").as("patientNames");
        
        // Project operation to shape the output
        ProjectionOperation projectionOperation = Aggregation.project()
            .and("_id").as("ssnGroup")
            .and("ssn").as("ssn")
            .and("patientCount").as("count")
            .and("patientNames").as("patients");
        
        // Create the aggregation pipeline
        Aggregation aggregation = Aggregation.newAggregation(
            matchOperation,
            groupOperation,
            projectionOperation
        );
        
        // Execute the aggregation
        AggregationResults<Map> results = mongoTemplate.aggregate(
            aggregation, "patients", Map.class
        );
        
        return results.getMappedResults();
    }

    public List<Map> findPatientsByCardTypeWithStatistics(String cardType) {
        logger.debug("Finding patients by card type with statistics: {}", cardType);
        
        // Match operation to filter by card type
        MatchOperation matchOperation = Aggregation.match(
            Criteria.where("record.billing.cardType").is(cardType)
        );
        
        // Add fields operation to calculate derived fields
        AddFieldsOperation addFieldsOperation = Aggregation.addFields()
            .addField("cardTypeUpperCase")
            .withValue(StringOperators.ToUpper.upperValueOf("$record.billing.cardType"))
            .build();
        
        // Group operation for statistics
        GroupOperation groupOperation = Aggregation.group("record.billing.cardType")
            .count().as("totalPatients")
            .addToSet("record.ssn").as("uniqueSSNs")
            .addToSet("name").as("patientNames");
        
        // Project operation to shape the final output
        ProjectionOperation projectionOperation = Aggregation.project()
            .and("_id").as("cardType")
            .and("totalPatients").as("count")
            .and("uniqueSSNs").size().as("uniqueSSNCount")
            .and("patientNames").as("patients");
        
        // Create the aggregation pipeline
        Aggregation aggregation = Aggregation.newAggregation(
            matchOperation,
            addFieldsOperation,
            groupOperation,
            projectionOperation,
            Aggregation.sort(Sort.Direction.DESC, "count")
        );
        
        // Execute the aggregation
        AggregationResults<Map> results = mongoTemplate.aggregate(
            aggregation, "patients", Map.class
        );
        
        return results.getMappedResults();
    }

    public List<Patient> searchPatients(String searchText) {
        logger.debug("Searching patients with text: {}", searchText);

        org.springframework.data.mongodb.core.query.TextCriteria criteria =
            org.springframework.data.mongodb.core.query.TextCriteria.forDefaultLanguage().matching(searchText);

        org.springframework.data.mongodb.core.query.Query query =
            org.springframework.data.mongodb.core.query.Query.query(criteria);

        return mongoTemplate.find(query, Patient.class);
    }
}
