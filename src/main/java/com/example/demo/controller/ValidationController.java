package com.example.demo.controller;

import com.example.demo.service.ValidationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for XML validation using PHIVE
 * 
 * This controller provides endpoints for:
 * - XML validation with comprehensive business rules
 * - Retrieving available validation executor sets
 * - Health checks and service status
 */
@RestController
@RequestMapping("/api/v1/validation")
@RequiredArgsConstructor
@Validated
@Slf4j
public class ValidationController {

    private final ValidationService validationService;

    /**
     * Main validation endpoint
     * 
     * Validates XML content using the specified validation executor set.
     * Supports multi-layer validation including XSD and Schematron rules.
     */
    @PostMapping("/validate")
    public ResponseEntity<ValidationResponse> validateXml(@Valid @RequestBody ValidationRequest request) {
        log.info("Received validation request for executor set: {}", request.getValidationExecutorSetId());
        
        try {
            ValidationResponse response = validationService.validateXml(request);
            
            // Return appropriate HTTP status based on validation result
            HttpStatus status = response.isSuccess() ? HttpStatus.OK : HttpStatus.BAD_REQUEST;
            
            return ResponseEntity.status(status).body(response);
            
        } catch (Exception e) {
            log.error("Error processing validation request", e);
            
            ValidationResponse errorResponse = ValidationResponse.builder()
                .success(false)
                .validationExecutorSetId(request.getValidationExecutorSetId())
                .errorMessage("Internal server error: " + e.getMessage())
                .timestamp(LocalDateTime.now())
                .build();
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get all available validation executor sets
     */
    @GetMapping("/executor-sets")
    public ResponseEntity<List<ValidationService.ValidationExecutorSetInfo>> getAvailableValidationSets() {
        log.info("Retrieving available validation executor sets");
        
        try {
            List<ValidationService.ValidationExecutorSetInfo> validationSets = 
                validationService.getAvailableValidationSets();
            
            return ResponseEntity.ok(validationSets);
            
        } catch (Exception e) {
            log.error("Error retrieving validation executor sets", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get information about a specific validation executor set
     */
    @GetMapping("/executor-sets/{id}")
    public ResponseEntity<ValidationService.ValidationExecutorSetInfo> getValidationSetInfo(
            @PathVariable("id") String validationExecutorSetId) {
        
        log.info("Retrieving information for validation executor set: {}", validationExecutorSetId);
        
        try {
            ValidationService.ValidationExecutorSetInfo info = 
                validationService.getValidationSetInfo(validationExecutorSetId);
            
            if (info == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(info);
            
        } catch (Exception e) {
            log.error("Error retrieving validation executor set info", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        log.debug("Health check requested");
        
        try {
            List<ValidationService.ValidationExecutorSetInfo> validationSets = 
                validationService.getAvailableValidationSets();
            
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("timestamp", LocalDateTime.now());
            health.put("service", "PHIVE Validation API");
            health.put("version", "1.0.0");
            health.put("availableValidationSets", validationSets.size());
            health.put("validationSets", validationSets);
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            log.error("Health check failed", e);
            
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("timestamp", LocalDateTime.now());
            health.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(health);
        }
    }

    /**
     * Validate XML with simple string input (for testing)
     */
    @PostMapping("/validate-simple")
    public ResponseEntity<ValidationResponse> validateXmlSimple(
            @RequestParam("xml") String xmlContent,
            @RequestParam("validationSet") String validationExecutorSetId) {
        
        log.info("Received simple validation request for executor set: {}", validationExecutorSetId);
        
        ValidationRequest request = ValidationRequest.builder()
            .xmlContent(xmlContent)
            .validationExecutorSetId(validationExecutorSetId)
            .build();
        
        return validateXml(request);
    }

    /**
     * Check if a validation executor set is available
     */
    @GetMapping("/executor-sets/{id}/available")
    public ResponseEntity<Map<String, Object>> checkValidationSetAvailability(
            @PathVariable("id") String validationExecutorSetId) {
        
        log.info("Checking availability for validation executor set: {}", validationExecutorSetId);
        
        try {
            boolean available = validationService.isValidationSetAvailable(validationExecutorSetId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("validationExecutorSetId", validationExecutorSetId);
            result.put("available", available);
            result.put("timestamp", LocalDateTime.now());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("Error checking validation executor set availability", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get validation statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getValidationStatistics() {
        log.info("Retrieving validation statistics");
        
        try {
            List<ValidationService.ValidationExecutorSetInfo> validationSets = 
                validationService.getAvailableValidationSets();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalValidationSets", validationSets.size());
            stats.put("timestamp", LocalDateTime.now());
            
            // Group by validation types
            Map<String, Integer> typeStats = new HashMap<>();
            validationSets.forEach(set -> {
                String type = determineValidationType(set.getId());
                typeStats.put(type, typeStats.getOrDefault(type, 0) + 1);
            });
            stats.put("validationSetsByType", typeStats);
            
            return ResponseEntity.ok(stats);
            
        } catch (Exception e) {
            log.error("Error retrieving validation statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Determine validation type from executor set ID
     */
    private String determineValidationType(String executorSetId) {
        if (executorSetId.contains("einvoice") || executorSetId.contains("invoice")) {
            return "E-Invoice";
        } else if (executorSetId.contains("creditnote")) {
            return "Credit Note";
        } else if (executorSetId.contains("order")) {
            return "Purchase Order";
        } else if (executorSetId.contains("peppol")) {
            return "Peppol";
        } else if (executorSetId.contains("basic") || executorSetId.contains("simple")) {
            return "Basic";
        } else {
            return "Custom";
        }
    }
}
