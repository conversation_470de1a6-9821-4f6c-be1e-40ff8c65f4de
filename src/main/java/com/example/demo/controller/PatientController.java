package com.example.demo.controller;

import com.example.demo.model.Patient;
import com.example.demo.service.PatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/patients")
public class PatientController {
    
    @Autowired
    private PatientService patientService;
    
    @PostMapping
    public ResponseEntity<Patient> createPatient(@RequestBody Patient patient) {
        return new ResponseEntity<>(patientService.savePatient(patient), HttpStatus.CREATED);
    }
    
    @GetMapping
    public ResponseEntity<List<Patient>> getAllPatients() {
        return new ResponseEntity<>(patientService.getAllPatients(), HttpStatus.OK);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<Patient> getPatientById(@PathVariable String id) {
        return patientService.getPatientById(id)
                .map( patient -> new ResponseEntity<>(patient, HttpStatus.OK))
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<Patient> updatePatient(@PathVariable String id, @RequestBody Patient patient) {
        return patientService.getPatientById(id)
                .map(existingPatient -> {
                    patient.setId(id);
                    return new ResponseEntity<>(patientService.updatePatient(patient), HttpStatus.OK);
                })
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePatient(@PathVariable String id) {
        return patientService.getPatientById(id)
                .map(patient -> {
                    patientService.deletePatient(id);
                    return new ResponseEntity<Void>(HttpStatus.NO_CONTENT);
                })
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    @GetMapping("/search/card-type/{cardType}")
    public ResponseEntity<List<Patient>> getPatientsByCardType(@PathVariable String cardType) {
        List<Patient> patients = patientService.findPatientsByCardType(cardType);
        if (patients.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(patients, HttpStatus.OK);
    }

    @GetMapping("/search/name/{name}")
    public ResponseEntity<List<Patient>> getPatientByName(@PathVariable String name) {
        List<Patient> patients = patientService.findPatientByName(name);
        if (patients.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(patients, HttpStatus.OK);
    }

    @GetMapping("/search/card-type-agg/{cardType}")
    public ResponseEntity<List<Patient>> getPatientsByCardTypeAggregation(@PathVariable String cardType) {
        List<Patient> patients = patientService.findPatientsByCardTypeAggregation(cardType);
        if (patients.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(patients, HttpStatus.OK);
    }

    @GetMapping("/search/card-type-advanced/{cardType}")
    public ResponseEntity<List<Patient>> getPatientsByCardTypeAdvancedAggregation(@PathVariable String cardType) {
        List<Patient> patients = patientService.findPatientsByCardTypeAdvancedAggregation(cardType);
        if (patients.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(patients, HttpStatus.OK);
    }

    @GetMapping("/search/card-type-group-ssn/{cardType}")
    public ResponseEntity<List<Map>> getPatientsByCardTypeGroupBySSN(@PathVariable String cardType) {
        List<Map> results = patientService.findPatientsByCardTypeGroupBySSN(cardType);
        if (results.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(results, HttpStatus.OK);
    }

    @GetMapping("/search/card-type-stats/{cardType}")
    public ResponseEntity<List<Map>> getPatientsByCardTypeWithStatistics(@PathVariable String cardType) {
        List<Map> results = patientService.findPatientsByCardTypeWithStatistics(cardType);
        if (results.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(results, HttpStatus.OK);
    }

    @GetMapping("/search/text/{searchText}")
    public ResponseEntity<List<Patient>> searchPatients(@PathVariable String searchText) {
        List<Patient> patients = patientService.searchPatients(searchText);
        if (patients.isEmpty()) {
            return new ResponseEntity<>(HttpStatus.NO_CONTENT);
        }
        return new ResponseEntity<>(patients, HttpStatus.OK);
    }
}
