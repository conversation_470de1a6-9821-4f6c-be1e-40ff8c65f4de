package com.example.demo.controller;

import com.example.demo.model.QueryRequest;
import com.example.demo.model.UploadRequest;
import com.example.demo.service.DocumentEmbeddingService;
import com.example.demo.service.RagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/rag")
public class RagController {

    private final DocumentEmbeddingService documentEmbeddingService;
    private final RagService ragService;
    private String apiKey;
    @Autowired
    public RagController(DocumentEmbeddingService documentEmbeddingService, RagService ragService) {
        this.documentEmbeddingService = documentEmbeddingService;
        this.ragService = ragService;
        this.apiKey = "********************************************************\n";
    }

    /**
     * Endpoint to upload a document to the dataset
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadDocument(
            @RequestBody UploadRequest request) {
        
        try {
            // Store API key for future use
            this.apiKey = request.getApiKey();
            
            // Process and embed the document
            List<String> processingInfo = documentEmbeddingService.processDocumentAndStoreEmbeddings(
                    request.getApiKey(),
                    request.getDataset(),
                    request.getUrl(),
                    request.getTestQuestion()
            );
            
            Map<String, Object> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "Document processed and embedded successfully");
            response.put("details", processingInfo);
            
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to process document: " + e.getMessage());
            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Endpoint to query the RAG system
     */
    @PostMapping("/query")
    public ResponseEntity<Map<String, Object>> queryRag(
            @RequestBody QueryRequest request) {
        
        try {
            // Use stored API key or the one provided in the request
            String key = (request.getApiKey() != null) ? request.getApiKey() : this.apiKey;
            
            if (key == null) {
                return new ResponseEntity<>(
                        Map.of("status", "error", "message", "API key is required"),
                        HttpStatus.BAD_REQUEST
                );
            }
            
            // Generate response using RAG
            String response = ragService.generateResponse(
                    key,
                    request.getDataset(),
                    request.getQuestion()
            );
            
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("status", "success");
            responseMap.put("response", response);
            
            return new ResponseEntity<>(responseMap, HttpStatus.OK);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "error");
            response.put("message", "Failed to process query: " + e.getMessage());
            
            return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}