package com.example.demo.repository;

import com.example.demo.model.Patient;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface PatientRepository extends MongoRepository<Patient, String> {

    @Query("{'record.billing.cardType': ?0}")
    List<Patient> findByCardType(String cardType);

    @Aggregation(pipeline = {
        "{ $match: { 'record.billing.cardType': ?0 } }",
        "{ $project: { '_id': 1, 'name': 1, 'record.billing.cardType': 1 } }"
    })
    List<Patient> findPatientsByCardTypeAggregation(String cardType);

    List<Patient> findByName(String name);
}
