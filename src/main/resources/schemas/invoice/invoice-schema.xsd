<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="urn:example:invoice"
           xmlns:tns="urn:example:invoice"
           elementFormDefault="qualified">

    <!-- Invoice Schema for PHIVE Validation -->
    
    <xs:element name="Invoice" type="tns:InvoiceType"/>
    
    <xs:complexType name="InvoiceType">
        <xs:sequence>
            <xs:element name="InvoiceHeader" type="tns:InvoiceHeaderType"/>
            <xs:element name="InvoiceLines" type="tns:InvoiceLinesType"/>
            <xs:element name="InvoiceTotals" type="tns:InvoiceTotalsType"/>
        </xs:sequence>
        <xs:attribute name="version" type="xs:string" use="required"/>
        <xs:attribute name="id" type="xs:string" use="required"/>
    </xs:complexType>
    
    <xs:complexType name="InvoiceHeaderType">
        <xs:sequence>
            <xs:element name="InvoiceNumber" type="xs:string"/>
            <xs:element name="InvoiceDate" type="xs:date"/>
            <xs:element name="DueDate" type="xs:date"/>
            <xs:element name="Currency" type="tns:CurrencyType"/>
            <xs:element name="Supplier" type="tns:PartyType"/>
            <xs:element name="Customer" type="tns:PartyType"/>
            <xs:element name="PaymentTerms" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="InvoiceLinesType">
        <xs:sequence>
            <xs:element name="InvoiceLine" type="tns:InvoiceLineType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="InvoiceLineType">
        <xs:sequence>
            <xs:element name="LineNumber" type="xs:positiveInteger"/>
            <xs:element name="ItemId" type="xs:string"/>
            <xs:element name="ItemName" type="xs:string"/>
            <xs:element name="ItemDescription" type="xs:string" minOccurs="0"/>
            <xs:element name="Quantity" type="xs:decimal"/>
            <xs:element name="UnitPrice" type="xs:decimal"/>
            <xs:element name="LineTotal" type="xs:decimal"/>
            <xs:element name="TaxRate" type="xs:decimal"/>
            <xs:element name="TaxAmount" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="InvoiceTotalsType">
        <xs:sequence>
            <xs:element name="SubTotal" type="xs:decimal"/>
            <xs:element name="TotalTax" type="xs:decimal"/>
            <xs:element name="TotalAmount" type="xs:decimal"/>
            <xs:element name="AmountDue" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="PartyType">
        <xs:sequence>
            <xs:element name="Name" type="xs:string"/>
            <xs:element name="Id" type="xs:string"/>
            <xs:element name="TaxId" type="xs:string" minOccurs="0"/>
            <xs:element name="Address" type="tns:AddressType"/>
            <xs:element name="Contact" type="tns:ContactType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="AddressType">
        <xs:sequence>
            <xs:element name="Street" type="xs:string"/>
            <xs:element name="City" type="xs:string"/>
            <xs:element name="PostalCode" type="xs:string"/>
            <xs:element name="Country" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="ContactType">
        <xs:sequence>
            <xs:element name="Email" type="xs:string" minOccurs="0"/>
            <xs:element name="Phone" type="xs:string" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:simpleType name="CurrencyType">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3}"/>
        </xs:restriction>
    </xs:simpleType>
    
</xs:schema>
