<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
           targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
           elementFormDefault="qualified">

  <!-- Basic String Types -->
  <xs:simpleType name="TextType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>

  <xs:simpleType name="NameType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>

  <xs:simpleType name="IdentifierType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>

  <xs:simpleType name="CodeType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>

  <!-- Numeric Types -->
  <xs:simpleType name="AmountType">
    <xs:restriction base="xs:decimal"/>
  </xs:simpleType>

  <xs:simpleType name="QuantityType">
    <xs:restriction base="xs:decimal"/>
  </xs:simpleType>

  <xs:simpleType name="NumericType">
    <xs:restriction base="xs:decimal"/>
  </xs:simpleType>

  <xs:simpleType name="PercentType">
    <xs:restriction base="xs:decimal"/>
  </xs:simpleType>

  <!-- Date Types -->
  <xs:simpleType name="DateType">
    <xs:restriction base="xs:date"/>
  </xs:simpleType>

  <xs:simpleType name="TimeType">
    <xs:restriction base="xs:time"/>
  </xs:simpleType>

  <!-- Basic Elements -->
  <xs:element name="UBLVersionID" type="IdentifierType"/>
  <xs:element name="CustomizationID" type="IdentifierType"/>
  <xs:element name="ProfileID" type="IdentifierType"/>
  <xs:element name="ID" type="IdentifierType"/>
  <xs:element name="IssueDate" type="DateType"/>
  <xs:element name="DueDate" type="DateType"/>
  <xs:element name="InvoiceTypeCode" type="CodeType"/>
  <xs:element name="Note" type="TextType"/>
  <xs:element name="TaxPointDate" type="DateType"/>
  <xs:element name="DocumentCurrencyCode" type="CodeType"/>
  <xs:element name="TaxCurrencyCode" type="CodeType"/>
  <xs:element name="AccountingCost" type="TextType"/>
  <xs:element name="BuyerReference" type="TextType"/>

  <!-- Party Elements -->
  <xs:element name="Name" type="NameType"/>
  <xs:element name="CompanyID" type="IdentifierType"/>
  <xs:element name="RegistrationName" type="NameType"/>
  <xs:element name="TaxSchemeID" type="IdentifierType"/>
  <xs:element name="TaxSchemeName" type="TextType"/>

  <!-- Address Elements -->
  <xs:element name="StreetName" type="TextType"/>
  <xs:element name="AdditionalStreetName" type="TextType"/>
  <xs:element name="CityName" type="TextType"/>
  <xs:element name="PostalZone" type="TextType"/>
  <xs:element name="CountrySubentity" type="TextType"/>
  <xs:element name="IdentificationCode" type="CodeType"/>

  <!-- Contact Elements -->
  <xs:element name="Telephone" type="TextType"/>
  <xs:element name="Telefax" type="TextType"/>
  <xs:element name="ElectronicMail" type="TextType"/>

  <!-- Line Elements -->
  <xs:element name="LineID" type="IdentifierType"/>
  <xs:element name="InvoicedQuantity" type="QuantityType"/>
  <xs:element name="LineExtensionAmount" type="AmountType"/>

  <!-- Item Elements -->
  <xs:element name="Description" type="TextType"/>
  <xs:element name="SellersItemIdentification" type="IdentifierType"/>
  <xs:element name="BuyersItemIdentification" type="IdentifierType"/>
  <xs:element name="StandardItemIdentification" type="IdentifierType"/>

  <!-- Price Elements -->
  <xs:element name="PriceAmount" type="AmountType"/>
  <xs:element name="BaseQuantity" type="QuantityType"/>

  <!-- Tax Elements -->
  <xs:element name="TaxableAmount" type="AmountType"/>
  <xs:element name="TaxAmount" type="AmountType"/>
  <xs:element name="TaxCategoryID" type="IdentifierType"/>
  <xs:element name="Percent" type="PercentType"/>
  <xs:element name="TaxExemptionReason" type="TextType"/>

  <!-- Monetary Elements -->
  <xs:element name="LineExtensionTotalAmount" type="AmountType"/>
  <xs:element name="TaxExclusiveAmount" type="AmountType"/>
  <xs:element name="TaxInclusiveAmount" type="AmountType"/>
  <xs:element name="AllowanceTotalAmount" type="AmountType"/>
  <xs:element name="ChargeTotalAmount" type="AmountType"/>
  <xs:element name="PrepaidAmount" type="AmountType"/>
  <xs:element name="PayableRoundingAmount" type="AmountType"/>
  <xs:element name="PayableAmount" type="AmountType"/>

  <!-- Allowance/Charge Elements -->
  <xs:element name="ChargeIndicator" type="xs:boolean"/>
  <xs:element name="AllowanceChargeReason" type="TextType"/>
  <xs:element name="AllowanceChargeReasonCode" type="CodeType"/>
  <xs:element name="MultiplierFactorNumeric" type="NumericType"/>
  <xs:element name="Amount" type="AmountType"/>
  <xs:element name="BaseAmount" type="AmountType"/>

  <!-- Payment Elements -->
  <xs:element name="PaymentMeansCode" type="CodeType"/>
  <xs:element name="PaymentMeansText" type="TextType"/>
  <xs:element name="PaymentID" type="IdentifierType"/>
  <xs:element name="PrimaryAccountNumberID" type="IdentifierType"/>
  <xs:element name="NetworkID" type="IdentifierType"/>

  <!-- Reference Elements -->
  <xs:element name="DocumentTypeCode" type="CodeType"/>
  <xs:element name="DocumentDescription" type="TextType"/>

</xs:schema>
