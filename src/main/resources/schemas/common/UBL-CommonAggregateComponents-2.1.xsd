<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
           xmlns="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
           targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
           elementFormDefault="qualified">

  <xs:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" 
             schemaLocation="UBL-CommonBasicComponents-2.1.xsd"/>

  <!-- Party Type -->
  <xs:complexType name="PartyType">
    <xs:sequence>
      <xs:element ref="cbc:Name" minOccurs="0"/>
      <xs:element ref="PartyIdentification" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="PartyName" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="PostalAddress" minOccurs="0"/>
      <xs:element ref="PartyTaxScheme" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="PartyLegalEntity" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="Contact" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Party Identification Type -->
  <xs:complexType name="PartyIdentificationType">
    <xs:sequence>
      <xs:element ref="cbc:ID"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Party Name Type -->
  <xs:complexType name="PartyNameType">
    <xs:sequence>
      <xs:element ref="cbc:Name"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Postal Address Type -->
  <xs:complexType name="PostalAddressType">
    <xs:sequence>
      <xs:element ref="cbc:StreetName" minOccurs="0"/>
      <xs:element ref="cbc:AdditionalStreetName" minOccurs="0"/>
      <xs:element ref="cbc:CityName" minOccurs="0"/>
      <xs:element ref="cbc:PostalZone" minOccurs="0"/>
      <xs:element ref="cbc:CountrySubentity" minOccurs="0"/>
      <xs:element ref="Country" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Country Type -->
  <xs:complexType name="CountryType">
    <xs:sequence>
      <xs:element ref="cbc:IdentificationCode"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Party Tax Scheme Type -->
  <xs:complexType name="PartyTaxSchemeType">
    <xs:sequence>
      <xs:element ref="cbc:RegistrationName" minOccurs="0"/>
      <xs:element ref="cbc:CompanyID" minOccurs="0"/>
      <xs:element ref="TaxScheme"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Scheme Type -->
  <xs:complexType name="TaxSchemeType">
    <xs:sequence>
      <xs:element ref="cbc:ID"/>
      <xs:element ref="cbc:Name" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Party Legal Entity Type -->
  <xs:complexType name="PartyLegalEntityType">
    <xs:sequence>
      <xs:element ref="cbc:RegistrationName" minOccurs="0"/>
      <xs:element ref="cbc:CompanyID" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Contact Type -->
  <xs:complexType name="ContactType">
    <xs:sequence>
      <xs:element ref="cbc:Name" minOccurs="0"/>
      <xs:element ref="cbc:Telephone" minOccurs="0"/>
      <xs:element ref="cbc:Telefax" minOccurs="0"/>
      <xs:element ref="cbc:ElectronicMail" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Supplier Party Type -->
  <xs:complexType name="SupplierPartyType">
    <xs:sequence>
      <xs:element ref="Party"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Customer Party Type -->
  <xs:complexType name="CustomerPartyType">
    <xs:sequence>
      <xs:element ref="Party"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Invoice Line Type -->
  <xs:complexType name="InvoiceLineType">
    <xs:sequence>
      <xs:element ref="cbc:ID"/>
      <xs:element ref="cbc:Note" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="cbc:InvoicedQuantity"/>
      <xs:element ref="cbc:LineExtensionAmount"/>
      <xs:element ref="cbc:AccountingCost" minOccurs="0"/>
      <xs:element ref="InvoicePeriod" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="OrderLineReference" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="DespatchLineReference" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="ReceiptLineReference" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="BillingReference" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="DocumentReference" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="PricingReference" minOccurs="0"/>
      <xs:element ref="AllowanceCharge" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="TaxTotal" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="WithholdingTaxTotal" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="Item"/>
      <xs:element ref="Price"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Item Type -->
  <xs:complexType name="ItemType">
    <xs:sequence>
      <xs:element ref="cbc:Description" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="cbc:Name" minOccurs="0"/>
      <xs:element ref="SellersItemIdentification" minOccurs="0"/>
      <xs:element ref="BuyersItemIdentification" minOccurs="0"/>
      <xs:element ref="StandardItemIdentification" minOccurs="0"/>
      <xs:element ref="ItemSpecificationDocumentReference" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="OriginCountry" minOccurs="0"/>
      <xs:element ref="CommodityClassification" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="TransactionConditions" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="HazardousItem" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="ClassifiedTaxCategory" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="AdditionalItemProperty" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="ManufacturerParty" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="InformationContentProviderParty" minOccurs="0"/>
      <xs:element ref="OriginAddress" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="ItemInstance" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Price Type -->
  <xs:complexType name="PriceType">
    <xs:sequence>
      <xs:element ref="cbc:PriceAmount"/>
      <xs:element ref="cbc:BaseQuantity" minOccurs="0"/>
      <xs:element ref="PriceChangeReason" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="PriceTypeCode" minOccurs="0"/>
      <xs:element ref="PriceType" minOccurs="0"/>
      <xs:element ref="OrderableUnitFactorRate" minOccurs="0"/>
      <xs:element ref="ValidityPeriod" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="PriceList" minOccurs="0"/>
      <xs:element ref="AllowanceCharge" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="PricingExchangeRate" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Total Type -->
  <xs:complexType name="TaxTotalType">
    <xs:sequence>
      <xs:element ref="cbc:TaxAmount"/>
      <xs:element ref="cbc:RoundingAmount" minOccurs="0"/>
      <xs:element ref="cbc:TaxEvidenceIndicator" minOccurs="0"/>
      <xs:element ref="cbc:TaxIncludedIndicator" minOccurs="0"/>
      <xs:element ref="TaxSubtotal" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Subtotal Type -->
  <xs:complexType name="TaxSubtotalType">
    <xs:sequence>
      <xs:element ref="cbc:TaxableAmount" minOccurs="0"/>
      <xs:element ref="cbc:TaxAmount"/>
      <xs:element ref="cbc:CalculationSequenceNumeric" minOccurs="0"/>
      <xs:element ref="cbc:TransactionCurrencyTaxAmount" minOccurs="0"/>
      <xs:element ref="cbc:Percent" minOccurs="0"/>
      <xs:element ref="cbc:BaseUnitMeasure" minOccurs="0"/>
      <xs:element ref="cbc:PerUnitAmount" minOccurs="0"/>
      <xs:element ref="cbc:TierRange" minOccurs="0"/>
      <xs:element ref="cbc:TierRatePercent" minOccurs="0"/>
      <xs:element ref="TaxCategory"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Category Type -->
  <xs:complexType name="TaxCategoryType">
    <xs:sequence>
      <xs:element ref="cbc:ID"/>
      <xs:element ref="cbc:Name" minOccurs="0"/>
      <xs:element ref="cbc:Percent" minOccurs="0"/>
      <xs:element ref="cbc:BaseUnitMeasure" minOccurs="0"/>
      <xs:element ref="cbc:PerUnitAmount" minOccurs="0"/>
      <xs:element ref="cbc:TaxExemptionReasonCode" minOccurs="0"/>
      <xs:element ref="cbc:TaxExemptionReason" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="cbc:TierRange" minOccurs="0"/>
      <xs:element ref="cbc:TierRatePercent" minOccurs="0"/>
      <xs:element ref="TaxScheme"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Legal Monetary Total Type -->
  <xs:complexType name="LegalMonetaryTotalType">
    <xs:sequence>
      <xs:element ref="cbc:LineExtensionAmount" minOccurs="0"/>
      <xs:element ref="cbc:TaxExclusiveAmount" minOccurs="0"/>
      <xs:element ref="cbc:TaxInclusiveAmount" minOccurs="0"/>
      <xs:element ref="cbc:AllowanceTotalAmount" minOccurs="0"/>
      <xs:element ref="cbc:ChargeTotalAmount" minOccurs="0"/>
      <xs:element ref="cbc:PrepaidAmount" minOccurs="0"/>
      <xs:element ref="cbc:PayableRoundingAmount" minOccurs="0"/>
      <xs:element ref="cbc:PayableAmount"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Element Declarations -->
  <xs:element name="Party" type="PartyType"/>
  <xs:element name="PartyIdentification" type="PartyIdentificationType"/>
  <xs:element name="PartyName" type="PartyNameType"/>
  <xs:element name="PostalAddress" type="PostalAddressType"/>
  <xs:element name="Country" type="CountryType"/>
  <xs:element name="PartyTaxScheme" type="PartyTaxSchemeType"/>
  <xs:element name="TaxScheme" type="TaxSchemeType"/>
  <xs:element name="PartyLegalEntity" type="PartyLegalEntityType"/>
  <xs:element name="Contact" type="ContactType"/>
  <xs:element name="AccountingSupplierParty" type="SupplierPartyType"/>
  <xs:element name="AccountingCustomerParty" type="CustomerPartyType"/>
  <xs:element name="PayeeParty" type="PartyType"/>
  <xs:element name="BuyerCustomerParty" type="CustomerPartyType"/>
  <xs:element name="SellerSupplierParty" type="SupplierPartyType"/>
  <xs:element name="TaxRepresentativeParty" type="PartyType"/>
  <xs:element name="InvoiceLine" type="InvoiceLineType"/>
  <xs:element name="Item" type="ItemType"/>
  <xs:element name="Price" type="PriceType"/>
  <xs:element name="TaxTotal" type="TaxTotalType"/>
  <xs:element name="TaxSubtotal" type="TaxSubtotalType"/>
  <xs:element name="TaxCategory" type="TaxCategoryType"/>
  <xs:element name="LegalMonetaryTotal" type="LegalMonetaryTotalType"/>

  <!-- Additional elements that are referenced but not fully defined here -->
  <xs:element name="InvoicePeriod" type="xs:anyType"/>
  <xs:element name="OrderReference" type="xs:anyType"/>
  <xs:element name="BillingReference" type="xs:anyType"/>
  <xs:element name="DespatchDocumentReference" type="xs:anyType"/>
  <xs:element name="ReceiptDocumentReference" type="xs:anyType"/>
  <xs:element name="OriginatorDocumentReference" type="xs:anyType"/>
  <xs:element name="ContractDocumentReference" type="xs:anyType"/>
  <xs:element name="AdditionalDocumentReference" type="xs:anyType"/>
  <xs:element name="ProjectReference" type="xs:anyType"/>
  <xs:element name="Delivery" type="xs:anyType"/>
  <xs:element name="DeliveryTerms" type="xs:anyType"/>
  <xs:element name="PaymentMeans" type="xs:anyType"/>
  <xs:element name="PaymentTerms" type="xs:anyType"/>
  <xs:element name="PrepaidPayment" type="xs:anyType"/>
  <xs:element name="AllowanceCharge" type="xs:anyType"/>
  <xs:element name="TaxExchangeRate" type="xs:anyType"/>
  <xs:element name="PricingExchangeRate" type="xs:anyType"/>
  <xs:element name="PaymentExchangeRate" type="xs:anyType"/>
  <xs:element name="PaymentAlternativeExchangeRate" type="xs:anyType"/>
  <xs:element name="WithholdingTaxTotal" type="xs:anyType"/>
  <xs:element name="OrderLineReference" type="xs:anyType"/>
  <xs:element name="DespatchLineReference" type="xs:anyType"/>
  <xs:element name="ReceiptLineReference" type="xs:anyType"/>
  <xs:element name="DocumentReference" type="xs:anyType"/>
  <xs:element name="PricingReference" type="xs:anyType"/>
  <xs:element name="SellersItemIdentification" type="xs:anyType"/>
  <xs:element name="BuyersItemIdentification" type="xs:anyType"/>
  <xs:element name="StandardItemIdentification" type="xs:anyType"/>
  <xs:element name="ItemSpecificationDocumentReference" type="xs:anyType"/>
  <xs:element name="OriginCountry" type="xs:anyType"/>
  <xs:element name="CommodityClassification" type="xs:anyType"/>
  <xs:element name="TransactionConditions" type="xs:anyType"/>
  <xs:element name="HazardousItem" type="xs:anyType"/>
  <xs:element name="ClassifiedTaxCategory" type="xs:anyType"/>
  <xs:element name="AdditionalItemProperty" type="xs:anyType"/>
  <xs:element name="ManufacturerParty" type="xs:anyType"/>
  <xs:element name="InformationContentProviderParty" type="xs:anyType"/>
  <xs:element name="OriginAddress" type="xs:anyType"/>
  <xs:element name="ItemInstance" type="xs:anyType"/>
  <xs:element name="PriceChangeReason" type="xs:anyType"/>
  <xs:element name="PriceTypeCode" type="xs:anyType"/>
  <xs:element name="PriceType" type="xs:anyType"/>
  <xs:element name="OrderableUnitFactorRate" type="xs:anyType"/>
  <xs:element name="ValidityPeriod" type="xs:anyType"/>
  <xs:element name="PriceList" type="xs:anyType"/>

</xs:schema>
