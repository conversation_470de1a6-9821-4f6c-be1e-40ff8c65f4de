<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="urn:example:custom"
           targetNamespace="urn:example:custom"
           elementFormDefault="qualified">
           
  <xs:element name="customDocument">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="metadata" type="MetadataType"/>
        <xs:element name="content" type="ContentType"/>
      </xs:sequence>
      <xs:attribute name="id" type="xs:string" use="required"/>
      <xs:attribute name="version" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>
  
  <xs:complexType name="MetadataType">
    <xs:sequence>
      <xs:element name="title" type="xs:string"/>
      <xs:element name="description" type="xs:string" minOccurs="0"/>
      <xs:element name="created" type="xs:dateTime"/>
      <xs:element name="modified" type="xs:dateTime" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  
  <xs:complexType name="ContentType">
    <xs:sequence>
      <xs:element name="data" type="xs:string"/>
      <xs:element name="attachments" type="AttachmentsType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  
  <xs:complexType name="AttachmentsType">
    <xs:sequence>
      <xs:element name="attachment" type="AttachmentType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  
  <xs:complexType name="AttachmentType">
    <xs:sequence>
      <xs:element name="name" type="xs:string"/>
      <xs:element name="type" type="xs:string"/>
      <xs:element name="size" type="xs:long"/>
    </xs:sequence>
  </xs:complexType>
  
</xs:schema>
