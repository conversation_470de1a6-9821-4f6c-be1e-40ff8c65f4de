<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns="urn:example:basic"
           targetNamespace="urn:example:basic"
           elementFormDefault="qualified">
           
  <xs:element name="document">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="header" type="HeaderType"/>
        <xs:element name="body" type="BodyType"/>
      </xs:sequence>
      <xs:attribute name="version" type="xs:string" use="required"/>
    </xs:complexType>
  </xs:element>
  
  <xs:complexType name="HeaderType">
    <xs:sequence>
      <xs:element name="title" type="xs:string"/>
      <xs:element name="author" type="xs:string" minOccurs="0"/>
      <xs:element name="date" type="xs:date" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>
  
  <xs:complexType name="BodyType">
    <xs:sequence>
      <xs:element name="section" type="SectionType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>
  
  <xs:complexType name="SectionType">
    <xs:sequence>
      <xs:element name="title" type="xs:string"/>
      <xs:element name="content" type="xs:string"/>
    </xs:sequence>
    <xs:attribute name="id" type="xs:ID" use="required"/>
  </xs:complexType>
  
</xs:schema>
