<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="urn:example:basic"
           xmlns:tns="urn:example:basic"
           elementFormDefault="qualified">

    <!-- Basic Document Schema for PHIVE Validation -->
    
    <xs:element name="Document" type="tns:DocumentType"/>
    
    <xs:complexType name="DocumentType">
        <xs:sequence>
            <xs:element name="Header" type="tns:HeaderType"/>
            <xs:element name="Body" type="tns:BodyType"/>
            <xs:element name="Footer" type="tns:FooterType" minOccurs="0"/>
        </xs:sequence>
        <xs:attribute name="version" type="xs:string" use="required"/>
        <xs:attribute name="id" type="xs:string" use="required"/>
    </xs:complexType>
    
    <xs:complexType name="HeaderType">
        <xs:sequence>
            <xs:element name="DocumentId" type="xs:string"/>
            <xs:element name="DocumentType" type="xs:string"/>
            <xs:element name="CreationDate" type="xs:dateTime"/>
            <xs:element name="Sender" type="tns:PartyType"/>
            <xs:element name="Receiver" type="tns:PartyType"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="BodyType">
        <xs:sequence>
            <xs:element name="Content" type="xs:string"/>
            <xs:element name="Items" type="tns:ItemsType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="FooterType">
        <xs:sequence>
            <xs:element name="Signature" type="xs:string" minOccurs="0"/>
            <xs:element name="Timestamp" type="xs:dateTime"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="PartyType">
        <xs:sequence>
            <xs:element name="Name" type="xs:string"/>
            <xs:element name="Id" type="xs:string"/>
            <xs:element name="Address" type="tns:AddressType" minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="AddressType">
        <xs:sequence>
            <xs:element name="Street" type="xs:string"/>
            <xs:element name="City" type="xs:string"/>
            <xs:element name="PostalCode" type="xs:string"/>
            <xs:element name="Country" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="ItemsType">
        <xs:sequence>
            <xs:element name="Item" type="tns:ItemType" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>
    
    <xs:complexType name="ItemType">
        <xs:sequence>
            <xs:element name="Id" type="xs:string"/>
            <xs:element name="Name" type="xs:string"/>
            <xs:element name="Description" type="xs:string" minOccurs="0"/>
            <xs:element name="Quantity" type="xs:decimal"/>
            <xs:element name="Price" type="xs:decimal"/>
        </xs:sequence>
    </xs:complexType>
    
</xs:schema>
