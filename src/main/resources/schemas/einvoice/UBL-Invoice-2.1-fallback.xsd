<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
           xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
           xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
           targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
           elementFormDefault="qualified">

  <!-- Simplified fallback schema for basic validation -->
  
  <!-- Root Invoice Element -->
  <xs:element name="Invoice" type="InvoiceType"/>

  <!-- Basic Types -->
  <xs:simpleType name="TextType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>

  <xs:simpleType name="IdentifierType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>

  <xs:simpleType name="CodeType">
    <xs:restriction base="xs:string"/>
  </xs:simpleType>

  <xs:simpleType name="AmountType">
    <xs:restriction base="xs:decimal"/>
  </xs:simpleType>

  <xs:simpleType name="QuantityType">
    <xs:restriction base="xs:decimal"/>
  </xs:simpleType>

  <xs:simpleType name="DateType">
    <xs:restriction base="xs:date"/>
  </xs:simpleType>

  <!-- Invoice Type Definition -->
  <xs:complexType name="InvoiceType">
    <xs:sequence>
      <xs:element name="UBLVersionID" type="IdentifierType" minOccurs="0"/>
      <xs:element name="CustomizationID" type="IdentifierType"/>
      <xs:element name="ProfileID" type="IdentifierType"/>
      <xs:element name="ID" type="IdentifierType"/>
      <xs:element name="IssueDate" type="DateType"/>
      <xs:element name="DueDate" type="DateType" minOccurs="0"/>
      <xs:element name="InvoiceTypeCode" type="CodeType"/>
      <xs:element name="Note" type="TextType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="DocumentCurrencyCode" type="CodeType"/>
      <xs:element name="BuyerReference" type="TextType" minOccurs="0"/>
      
      <xs:element name="InvoicePeriod" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="OrderReference" type="xs:anyType" minOccurs="0"/>
      
      <xs:element name="AccountingSupplierParty" type="PartyType"/>
      <xs:element name="AccountingCustomerParty" type="PartyType"/>
      
      <xs:element name="PaymentMeans" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="PaymentTerms" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
      
      <xs:element name="TaxTotal" type="TaxTotalType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="LegalMonetaryTotal" type="LegalMonetaryTotalType"/>
      <xs:element name="InvoiceLine" type="InvoiceLineType" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Party Type -->
  <xs:complexType name="PartyType">
    <xs:sequence>
      <xs:element name="EndpointID" type="IdentifierType" minOccurs="0"/>
      <xs:element name="PartyIdentification" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="PartyName" type="PartyNameType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="PostalAddress" type="AddressType" minOccurs="0"/>
      <xs:element name="PartyTaxScheme" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="PartyLegalEntity" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Contact" type="xs:anyType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Party Name Type -->
  <xs:complexType name="PartyNameType">
    <xs:sequence>
      <xs:element name="Name" type="TextType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Address Type -->
  <xs:complexType name="AddressType">
    <xs:sequence>
      <xs:element name="StreetName" type="TextType" minOccurs="0"/>
      <xs:element name="CityName" type="TextType" minOccurs="0"/>
      <xs:element name="PostalZone" type="TextType" minOccurs="0"/>
      <xs:element name="CountrySubentity" type="TextType" minOccurs="0"/>
      <xs:element name="Country" type="CountryType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Country Type -->
  <xs:complexType name="CountryType">
    <xs:sequence>
      <xs:element name="IdentificationCode" type="CodeType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Total Type -->
  <xs:complexType name="TaxTotalType">
    <xs:sequence>
      <xs:element name="TaxAmount" type="AmountType"/>
      <xs:element name="TaxSubtotal" type="TaxSubtotalType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Subtotal Type -->
  <xs:complexType name="TaxSubtotalType">
    <xs:sequence>
      <xs:element name="TaxableAmount" type="AmountType" minOccurs="0"/>
      <xs:element name="TaxAmount" type="AmountType"/>
      <xs:element name="TaxCategory" type="TaxCategoryType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Category Type -->
  <xs:complexType name="TaxCategoryType">
    <xs:sequence>
      <xs:element name="ID" type="IdentifierType"/>
      <xs:element name="Percent" type="xs:decimal" minOccurs="0"/>
      <xs:element name="TaxScheme" type="TaxSchemeType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Tax Scheme Type -->
  <xs:complexType name="TaxSchemeType">
    <xs:sequence>
      <xs:element name="ID" type="IdentifierType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Legal Monetary Total Type -->
  <xs:complexType name="LegalMonetaryTotalType">
    <xs:sequence>
      <xs:element name="LineExtensionAmount" type="AmountType" minOccurs="0"/>
      <xs:element name="TaxExclusiveAmount" type="AmountType" minOccurs="0"/>
      <xs:element name="TaxInclusiveAmount" type="AmountType" minOccurs="0"/>
      <xs:element name="PayableAmount" type="AmountType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Invoice Line Type -->
  <xs:complexType name="InvoiceLineType">
    <xs:sequence>
      <xs:element name="ID" type="IdentifierType"/>
      <xs:element name="Note" type="TextType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="InvoicedQuantity" type="QuantityType"/>
      <xs:element name="LineExtensionAmount" type="AmountType"/>
      <xs:element name="Item" type="ItemType"/>
      <xs:element name="Price" type="PriceType"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Item Type -->
  <xs:complexType name="ItemType">
    <xs:sequence>
      <xs:element name="Description" type="TextType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="Name" type="TextType" minOccurs="0"/>
      <xs:element name="SellersItemIdentification" type="xs:anyType" minOccurs="0"/>
      <xs:element name="CommodityClassification" type="xs:anyType" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element name="ClassifiedTaxCategory" type="TaxCategoryType" minOccurs="0" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

  <!-- Price Type -->
  <xs:complexType name="PriceType">
    <xs:sequence>
      <xs:element name="PriceAmount" type="AmountType"/>
      <xs:element name="BaseQuantity" type="QuantityType" minOccurs="0"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
