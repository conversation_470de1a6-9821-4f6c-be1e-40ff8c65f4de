<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
           xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
           xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
           targetNamespace="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
           elementFormDefault="qualified">

  <!-- Import common components -->
  <xs:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2" 
             schemaLocation="../common/UBL-CommonBasicComponents-2.1.xsd"/>
  <xs:import namespace="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2" 
             schemaLocation="../common/UBL-CommonAggregateComponents-2.1.xsd"/>

  <!-- Root Invoice Element -->
  <xs:element name="Invoice" type="InvoiceType"/>

  <!-- Invoice Type Definition -->
  <xs:complexType name="InvoiceType">
    <xs:sequence>
      <!-- Basic Invoice Information -->
      <xs:element ref="cbc:UBLVersionID" minOccurs="0"/>
      <xs:element ref="cbc:CustomizationID"/>
      <xs:element ref="cbc:ProfileID"/>
      <xs:element ref="cbc:ID"/>
      <xs:element ref="cbc:IssueDate"/>
      <xs:element ref="cbc:DueDate" minOccurs="0"/>
      <xs:element ref="cbc:InvoiceTypeCode"/>
      <xs:element ref="cbc:Note" minOccurs="0" maxOccurs="unbounded"/>
      <xs:element ref="cbc:TaxPointDate" minOccurs="0"/>
      <xs:element ref="cbc:DocumentCurrencyCode"/>
      <xs:element ref="cbc:TaxCurrencyCode" minOccurs="0"/>
      <xs:element ref="cbc:AccountingCost" minOccurs="0"/>
      <xs:element ref="cbc:BuyerReference" minOccurs="0"/>
      
      <!-- Invoice Period -->
      <xs:element ref="cac:InvoicePeriod" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Order Reference -->
      <xs:element ref="cac:OrderReference" minOccurs="0"/>
      
      <!-- Billing Reference -->
      <xs:element ref="cac:BillingReference" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Despatch Document Reference -->
      <xs:element ref="cac:DespatchDocumentReference" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Receipt Document Reference -->
      <xs:element ref="cac:ReceiptDocumentReference" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Originator Document Reference -->
      <xs:element ref="cac:OriginatorDocumentReference" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Contract Document Reference -->
      <xs:element ref="cac:ContractDocumentReference" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Additional Document Reference -->
      <xs:element ref="cac:AdditionalDocumentReference" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Project Reference -->
      <xs:element ref="cac:ProjectReference" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Parties -->
      <xs:element ref="cac:AccountingSupplierParty"/>
      <xs:element ref="cac:AccountingCustomerParty"/>
      <xs:element ref="cac:PayeeParty" minOccurs="0"/>
      <xs:element ref="cac:BuyerCustomerParty" minOccurs="0"/>
      <xs:element ref="cac:SellerSupplierParty" minOccurs="0"/>
      <xs:element ref="cac:TaxRepresentativeParty" minOccurs="0"/>
      
      <!-- Delivery -->
      <xs:element ref="cac:Delivery" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Delivery Terms -->
      <xs:element ref="cac:DeliveryTerms" minOccurs="0"/>
      
      <!-- Payment Means -->
      <xs:element ref="cac:PaymentMeans" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Payment Terms -->
      <xs:element ref="cac:PaymentTerms" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Prepaid Payment -->
      <xs:element ref="cac:PrepaidPayment" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Allowance Charge -->
      <xs:element ref="cac:AllowanceCharge" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Tax Exchange Rate -->
      <xs:element ref="cac:TaxExchangeRate" minOccurs="0"/>
      
      <!-- Pricing Exchange Rate -->
      <xs:element ref="cac:PricingExchangeRate" minOccurs="0"/>
      
      <!-- Payment Exchange Rate -->
      <xs:element ref="cac:PaymentExchangeRate" minOccurs="0"/>
      
      <!-- Payment Alternative Exchange Rate -->
      <xs:element ref="cac:PaymentAlternativeExchangeRate" minOccurs="0"/>
      
      <!-- Tax Total -->
      <xs:element ref="cac:TaxTotal" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Withholding Tax Total -->
      <xs:element ref="cac:WithholdingTaxTotal" minOccurs="0" maxOccurs="unbounded"/>
      
      <!-- Legal Monetary Total -->
      <xs:element ref="cac:LegalMonetaryTotal"/>
      
      <!-- Invoice Lines -->
      <xs:element ref="cac:InvoiceLine" maxOccurs="unbounded"/>
    </xs:sequence>
  </xs:complexType>

</xs:schema>
