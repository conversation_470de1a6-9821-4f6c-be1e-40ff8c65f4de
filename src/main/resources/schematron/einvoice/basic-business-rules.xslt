<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" 
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
                xmlns:ubl="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:svrl="http://purl.oclc.org/dsdl/svrl">

  <xsl:output method="xml" version="1.0" encoding="UTF-8" indent="yes"/>

  <!-- Root template -->
  <xsl:template match="/">
    <svrl:schematron-output title="E-Invoice Basic Business Rules Validation" 
                           schemaVersion="1.0">
      <xsl:apply-templates select="//ubl:Invoice"/>
    </svrl:schematron-output>
  </xsl:template>

  <!-- Main Invoice validation -->
  <xsl:template match="ubl:Invoice">
    
    <!-- Rule BR-01: Invoice must have an invoice number -->
    <xsl:if test="not(cbc:ID) or cbc:ID = ''">
      <svrl:failed-assert test="cbc:ID" location="Invoice">
        <svrl:text>BR-01: An Invoice must have an invoice number (cbc:ID).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-02: Invoice must have an issue date -->
    <xsl:if test="not(cbc:IssueDate) or cbc:IssueDate = ''">
      <svrl:failed-assert test="cbc:IssueDate" location="Invoice">
        <svrl:text>BR-02: An Invoice must have an issue date (cbc:IssueDate).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-03: Invoice must have an invoice type code -->
    <xsl:if test="not(cbc:InvoiceTypeCode) or cbc:InvoiceTypeCode = ''">
      <svrl:failed-assert test="cbc:InvoiceTypeCode" location="Invoice">
        <svrl:text>BR-03: An Invoice must have an invoice type code (cbc:InvoiceTypeCode).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-04: Invoice must have a document currency code -->
    <xsl:if test="not(cbc:DocumentCurrencyCode) or cbc:DocumentCurrencyCode = ''">
      <svrl:failed-assert test="cbc:DocumentCurrencyCode" location="Invoice">
        <svrl:text>BR-04: An Invoice must have a document currency code (cbc:DocumentCurrencyCode).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-05: Invoice must have a supplier party -->
    <xsl:if test="not(cac:AccountingSupplierParty)">
      <svrl:failed-assert test="cac:AccountingSupplierParty" location="Invoice">
        <svrl:text>BR-05: An Invoice must have a supplier party (cac:AccountingSupplierParty).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-06: Invoice must have a customer party -->
    <xsl:if test="not(cac:AccountingCustomerParty)">
      <svrl:failed-assert test="cac:AccountingCustomerParty" location="Invoice">
        <svrl:text>BR-06: An Invoice must have a customer party (cac:AccountingCustomerParty).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-07: Invoice must have at least one invoice line -->
    <xsl:if test="not(cac:InvoiceLine) or count(cac:InvoiceLine) = 0">
      <svrl:failed-assert test="cac:InvoiceLine" location="Invoice">
        <svrl:text>BR-07: An Invoice must have at least one invoice line (cac:InvoiceLine).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-08: Invoice must have legal monetary total -->
    <xsl:if test="not(cac:LegalMonetaryTotal)">
      <svrl:failed-assert test="cac:LegalMonetaryTotal" location="Invoice">
        <svrl:text>BR-08: An Invoice must have legal monetary total (cac:LegalMonetaryTotal).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-09: Supplier must have a name -->
    <xsl:if test="cac:AccountingSupplierParty and not(cac:AccountingSupplierParty/cac:Party/cac:PartyName/cbc:Name)">
      <svrl:failed-assert test="cac:AccountingSupplierParty/cac:Party/cac:PartyName/cbc:Name" location="Invoice/AccountingSupplierParty">
        <svrl:text>BR-09: Supplier must have a name (cac:PartyName/cbc:Name).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-10: Customer must have a name -->
    <xsl:if test="cac:AccountingCustomerParty and not(cac:AccountingCustomerParty/cac:Party/cac:PartyName/cbc:Name)">
      <svrl:failed-assert test="cac:AccountingCustomerParty/cac:Party/cac:PartyName/cbc:Name" location="Invoice/AccountingCustomerParty">
        <svrl:text>BR-10: Customer must have a name (cac:PartyName/cbc:Name).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-11: Invoice type code must be valid -->
    <xsl:if test="cbc:InvoiceTypeCode and not(cbc:InvoiceTypeCode = '380' or cbc:InvoiceTypeCode = '381' or cbc:InvoiceTypeCode = '384' or cbc:InvoiceTypeCode = '389')">
      <svrl:failed-assert test="cbc:InvoiceTypeCode" location="Invoice">
        <svrl:text>BR-11: Invoice type code must be one of: 380 (Commercial invoice), 381 (Credit note), 384 (Corrected invoice), 389 (Self-billed invoice).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-12: Currency code must be valid ISO 4217 -->
    <xsl:if test="cbc:DocumentCurrencyCode and not(string-length(cbc:DocumentCurrencyCode) = 3)">
      <svrl:failed-assert test="cbc:DocumentCurrencyCode" location="Invoice">
        <svrl:text>BR-12: Document currency code must be a valid 3-letter ISO 4217 currency code.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-13: Payable amount must be present -->
    <xsl:if test="cac:LegalMonetaryTotal and not(cac:LegalMonetaryTotal/cbc:PayableAmount)">
      <svrl:failed-assert test="cac:LegalMonetaryTotal/cbc:PayableAmount" location="Invoice/LegalMonetaryTotal">
        <svrl:text>BR-13: Legal monetary total must have a payable amount (cbc:PayableAmount).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-14: Tax exclusive amount must be present -->
    <xsl:if test="cac:LegalMonetaryTotal and not(cac:LegalMonetaryTotal/cbc:TaxExclusiveAmount)">
      <svrl:failed-assert test="cac:LegalMonetaryTotal/cbc:TaxExclusiveAmount" location="Invoice/LegalMonetaryTotal">
        <svrl:text>BR-14: Legal monetary total must have a tax exclusive amount (cbc:TaxExclusiveAmount).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-15: Tax inclusive amount must be present -->
    <xsl:if test="cac:LegalMonetaryTotal and not(cac:LegalMonetaryTotal/cbc:TaxInclusiveAmount)">
      <svrl:failed-assert test="cac:LegalMonetaryTotal/cbc:TaxInclusiveAmount" location="Invoice/LegalMonetaryTotal">
        <svrl:text>BR-15: Legal monetary total must have a tax inclusive amount (cbc:TaxInclusiveAmount).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Process invoice lines -->
    <xsl:apply-templates select="cac:InvoiceLine"/>

    <!-- Process tax totals -->
    <xsl:apply-templates select="cac:TaxTotal"/>

    <!-- Success message if no errors -->
    <xsl:if test="cbc:ID and cbc:IssueDate and cbc:InvoiceTypeCode and cbc:DocumentCurrencyCode and cac:AccountingSupplierParty and cac:AccountingCustomerParty and cac:InvoiceLine and cac:LegalMonetaryTotal">
      <svrl:successful-report test="true()" location="Invoice">
        <svrl:text>Basic business rules validation passed for Invoice <xsl:value-of select="cbc:ID"/>.</svrl:text>
      </svrl:successful-report>
    </xsl:if>

  </xsl:template>

  <!-- Invoice Line validation -->
  <xsl:template match="cac:InvoiceLine">
    
    <!-- Rule BR-21: Invoice line must have an ID -->
    <xsl:if test="not(cbc:ID) or cbc:ID = ''">
      <svrl:failed-assert test="cbc:ID" location="InvoiceLine">
        <svrl:text>BR-21: Invoice line must have an ID (cbc:ID).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-22: Invoice line must have invoiced quantity -->
    <xsl:if test="not(cbc:InvoicedQuantity)">
      <svrl:failed-assert test="cbc:InvoicedQuantity" location="InvoiceLine">
        <svrl:text>BR-22: Invoice line must have invoiced quantity (cbc:InvoicedQuantity).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-23: Invoice line must have line extension amount -->
    <xsl:if test="not(cbc:LineExtensionAmount)">
      <svrl:failed-assert test="cbc:LineExtensionAmount" location="InvoiceLine">
        <svrl:text>BR-23: Invoice line must have line extension amount (cbc:LineExtensionAmount).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-24: Invoice line must have an item -->
    <xsl:if test="not(cac:Item)">
      <svrl:failed-assert test="cac:Item" location="InvoiceLine">
        <svrl:text>BR-24: Invoice line must have an item (cac:Item).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-25: Invoice line must have a price -->
    <xsl:if test="not(cac:Price)">
      <svrl:failed-assert test="cac:Price" location="InvoiceLine">
        <svrl:text>BR-25: Invoice line must have a price (cac:Price).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-26: Item must have a name -->
    <xsl:if test="cac:Item and not(cac:Item/cbc:Name)">
      <svrl:failed-assert test="cac:Item/cbc:Name" location="InvoiceLine/Item">
        <svrl:text>BR-26: Item must have a name (cbc:Name).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-27: Price must have price amount -->
    <xsl:if test="cac:Price and not(cac:Price/cbc:PriceAmount)">
      <svrl:failed-assert test="cac:Price/cbc:PriceAmount" location="InvoiceLine/Price">
        <svrl:text>BR-27: Price must have price amount (cbc:PriceAmount).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-28: Invoiced quantity must be greater than zero -->
    <xsl:if test="cbc:InvoicedQuantity and number(cbc:InvoicedQuantity) &lt;= 0">
      <svrl:failed-assert test="cbc:InvoicedQuantity" location="InvoiceLine">
        <svrl:text>BR-28: Invoiced quantity must be greater than zero.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

  </xsl:template>

  <!-- Tax Total validation -->
  <xsl:template match="cac:TaxTotal">
    
    <!-- Rule BR-31: Tax total must have tax amount -->
    <xsl:if test="not(cbc:TaxAmount)">
      <svrl:failed-assert test="cbc:TaxAmount" location="TaxTotal">
        <svrl:text>BR-31: Tax total must have tax amount (cbc:TaxAmount).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule BR-32: Tax amount must be non-negative -->
    <xsl:if test="cbc:TaxAmount and number(cbc:TaxAmount) &lt; 0">
      <svrl:failed-assert test="cbc:TaxAmount" location="TaxTotal">
        <svrl:text>BR-32: Tax amount must be non-negative.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

  </xsl:template>

</xsl:stylesheet>
