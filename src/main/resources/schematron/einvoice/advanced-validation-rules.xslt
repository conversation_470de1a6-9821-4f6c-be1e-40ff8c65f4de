<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" 
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
                xmlns:ubl="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:svrl="http://purl.oclc.org/dsdl/svrl">

  <xsl:output method="xml" version="1.0" encoding="UTF-8" indent="yes"/>

  <!-- Root template -->
  <xsl:template match="/">
    <svrl:schematron-output title="E-Invoice Advanced Validation Rules" 
                           schemaVersion="1.0">
      <xsl:apply-templates select="//ubl:Invoice"/>
    </svrl:schematron-output>
  </xsl:template>

  <!-- Advanced Invoice validation -->
  <xsl:template match="ubl:Invoice">
    
    <!-- Rule AR-01: Calculation validation - Line extension amounts must sum to total -->
    <xsl:variable name="calculatedLineTotal" select="sum(cac:InvoiceLine/cbc:LineExtensionAmount)"/>
    <xsl:variable name="declaredLineTotal" select="cac:LegalMonetaryTotal/cbc:LineExtensionAmount"/>
    
    <xsl:if test="$declaredLineTotal and abs($calculatedLineTotal - number($declaredLineTotal)) > 0.01">
      <svrl:failed-assert test="cac:LegalMonetaryTotal/cbc:LineExtensionAmount" location="Invoice/LegalMonetaryTotal">
        <svrl:text>AR-01: Sum of invoice line amounts (<xsl:value-of select="$calculatedLineTotal"/>) must equal the declared line extension total (<xsl:value-of select="$declaredLineTotal"/>).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-02: Tax calculation validation -->
    <xsl:variable name="calculatedTaxTotal" select="sum(cac:TaxTotal/cbc:TaxAmount)"/>
    <xsl:variable name="taxExclusiveAmount" select="number(cac:LegalMonetaryTotal/cbc:TaxExclusiveAmount)"/>
    <xsl:variable name="taxInclusiveAmount" select="number(cac:LegalMonetaryTotal/cbc:TaxInclusiveAmount)"/>
    
    <xsl:if test="$taxExclusiveAmount and $taxInclusiveAmount and abs(($taxExclusiveAmount + $calculatedTaxTotal) - $taxInclusiveAmount) > 0.01">
      <svrl:failed-assert test="cac:LegalMonetaryTotal" location="Invoice/LegalMonetaryTotal">
        <svrl:text>AR-02: Tax exclusive amount (<xsl:value-of select="$taxExclusiveAmount"/>) plus tax total (<xsl:value-of select="$calculatedTaxTotal"/>) must equal tax inclusive amount (<xsl:value-of select="$taxInclusiveAmount"/>).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-03: Payable amount calculation -->
    <xsl:variable name="payableAmount" select="number(cac:LegalMonetaryTotal/cbc:PayableAmount)"/>
    <xsl:variable name="allowanceTotal" select="if (cac:LegalMonetaryTotal/cbc:AllowanceTotalAmount) then number(cac:LegalMonetaryTotal/cbc:AllowanceTotalAmount) else 0"/>
    <xsl:variable name="chargeTotal" select="if (cac:LegalMonetaryTotal/cbc:ChargeTotalAmount) then number(cac:LegalMonetaryTotal/cbc:ChargeTotalAmount) else 0"/>
    <xsl:variable name="prepaidAmount" select="if (cac:LegalMonetaryTotal/cbc:PrepaidAmount) then number(cac:LegalMonetaryTotal/cbc:PrepaidAmount) else 0"/>
    <xsl:variable name="roundingAmount" select="if (cac:LegalMonetaryTotal/cbc:PayableRoundingAmount) then number(cac:LegalMonetaryTotal/cbc:PayableRoundingAmount) else 0"/>
    
    <xsl:variable name="expectedPayable" select="$taxInclusiveAmount - $allowanceTotal + $chargeTotal - $prepaidAmount + $roundingAmount"/>
    
    <xsl:if test="$payableAmount and abs($payableAmount - $expectedPayable) > 0.01">
      <svrl:failed-assert test="cac:LegalMonetaryTotal/cbc:PayableAmount" location="Invoice/LegalMonetaryTotal">
        <svrl:text>AR-03: Payable amount (<xsl:value-of select="$payableAmount"/>) calculation error. Expected: <xsl:value-of select="$expectedPayable"/>.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-04: Supplier VAT identifier validation -->
    <xsl:if test="cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID">
      <xsl:variable name="vatId" select="cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID"/>
      <xsl:if test="not(matches($vatId, '^[A-Z]{2}[A-Z0-9]+$'))">
        <svrl:failed-assert test="cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme/cbc:CompanyID" location="Invoice/AccountingSupplierParty">
          <svrl:text>AR-04: Supplier VAT identifier must follow the format: 2-letter country code followed by alphanumeric characters.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule AR-05: Due date must be after issue date -->
    <xsl:if test="cbc:DueDate and cbc:IssueDate">
      <xsl:if test="xs:date(cbc:DueDate) &lt; xs:date(cbc:IssueDate)">
        <svrl:failed-assert test="cbc:DueDate" location="Invoice">
          <svrl:text>AR-05: Due date (<xsl:value-of select="cbc:DueDate"/>) must be on or after the issue date (<xsl:value-of select="cbc:IssueDate"/>).</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule AR-06: Invoice period validation -->
    <xsl:for-each select="cac:InvoicePeriod">
      <xsl:if test="cbc:StartDate and cbc:EndDate">
        <xsl:if test="xs:date(cbc:EndDate) &lt; xs:date(cbc:StartDate)">
          <svrl:failed-assert test="cbc:EndDate" location="Invoice/InvoicePeriod">
            <svrl:text>AR-06: Invoice period end date must be on or after the start date.</svrl:text>
          </svrl:failed-assert>
        </xsl:if>
      </xsl:if>
    </xsl:for-each>

    <!-- Rule AR-07: Payment means validation -->
    <xsl:for-each select="cac:PaymentMeans">
      <xsl:if test="cbc:PaymentMeansCode = '30' or cbc:PaymentMeansCode = '58'">
        <xsl:if test="not(cac:PayeeFinancialAccount/cbc:ID)">
          <svrl:failed-assert test="cac:PayeeFinancialAccount/cbc:ID" location="Invoice/PaymentMeans">
            <svrl:text>AR-07: Payment means code <xsl:value-of select="cbc:PaymentMeansCode"/> requires a payee financial account ID (IBAN/Account number).</svrl:text>
          </svrl:failed-assert>
        </xsl:if>
      </xsl:if>
    </xsl:for-each>

    <!-- Rule AR-08: Allowance/Charge validation -->
    <xsl:for-each select="cac:AllowanceCharge">
      <xsl:if test="not(cbc:ChargeIndicator)">
        <svrl:failed-assert test="cbc:ChargeIndicator" location="Invoice/AllowanceCharge">
          <svrl:text>AR-08: Allowance/Charge must have a charge indicator (true for charge, false for allowance).</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
      
      <xsl:if test="not(cbc:Amount)">
        <svrl:failed-assert test="cbc:Amount" location="Invoice/AllowanceCharge">
          <svrl:text>AR-08: Allowance/Charge must have an amount.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
      
      <xsl:if test="cbc:Amount and number(cbc:Amount) &lt;= 0">
        <svrl:failed-assert test="cbc:Amount" location="Invoice/AllowanceCharge">
          <svrl:text>AR-08: Allowance/Charge amount must be greater than zero.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:for-each>

    <!-- Process invoice lines for advanced validation -->
    <xsl:apply-templates select="cac:InvoiceLine"/>

    <!-- Process tax subtotals -->
    <xsl:apply-templates select="cac:TaxTotal/cac:TaxSubtotal"/>

  </xsl:template>

  <!-- Advanced Invoice Line validation -->
  <xsl:template match="cac:InvoiceLine">
    
    <!-- Rule AR-21: Line calculation validation -->
    <xsl:variable name="quantity" select="number(cbc:InvoicedQuantity)"/>
    <xsl:variable name="priceAmount" select="number(cac:Price/cbc:PriceAmount)"/>
    <xsl:variable name="lineAmount" select="number(cbc:LineExtensionAmount)"/>
    <xsl:variable name="baseQuantity" select="if (cac:Price/cbc:BaseQuantity) then number(cac:Price/cbc:BaseQuantity) else 1"/>
    
    <xsl:variable name="allowanceTotal" select="sum(cac:AllowanceCharge[cbc:ChargeIndicator='false']/cbc:Amount)"/>
    <xsl:variable name="chargeTotal" select="sum(cac:AllowanceCharge[cbc:ChargeIndicator='true']/cbc:Amount)"/>
    
    <xsl:variable name="expectedLineAmount" select="($quantity * $priceAmount div $baseQuantity) - $allowanceTotal + $chargeTotal"/>
    
    <xsl:if test="abs($lineAmount - $expectedLineAmount) > 0.01">
      <svrl:failed-assert test="cbc:LineExtensionAmount" location="InvoiceLine">
        <svrl:text>AR-21: Line <xsl:value-of select="cbc:ID"/> amount calculation error. Expected: <xsl:value-of select="$expectedLineAmount"/>, Actual: <xsl:value-of select="$lineAmount"/>.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-22: Item classification validation -->
    <xsl:if test="cac:Item/cac:CommodityClassification">
      <xsl:for-each select="cac:Item/cac:CommodityClassification">
        <xsl:if test="cbc:ItemClassificationCode and not(matches(cbc:ItemClassificationCode, '^[0-9]{4,10}$'))">
          <svrl:failed-assert test="cbc:ItemClassificationCode" location="InvoiceLine/Item/CommodityClassification">
            <svrl:text>AR-22: Item classification code must be 4-10 digits.</svrl:text>
          </svrl:failed-assert>
        </xsl:if>
      </xsl:for-each>
    </xsl:if>

    <!-- Rule AR-23: Unit of measure validation -->
    <xsl:if test="cbc:InvoicedQuantity/@unitCode">
      <xsl:variable name="unitCode" select="cbc:InvoicedQuantity/@unitCode"/>
      <xsl:if test="not(matches($unitCode, '^[A-Z0-9]{1,3}$'))">
        <svrl:failed-assert test="cbc:InvoicedQuantity/@unitCode" location="InvoiceLine">
          <svrl:text>AR-23: Unit of measure code must be 1-3 alphanumeric characters (UN/ECE Recommendation 20).</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule AR-24: Price validation -->
    <xsl:if test="cac:Price/cbc:PriceAmount and number(cac:Price/cbc:PriceAmount) &lt; 0">
      <svrl:failed-assert test="cac:Price/cbc:PriceAmount" location="InvoiceLine/Price">
        <svrl:text>AR-24: Price amount must be non-negative.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-25: Item identifier validation -->
    <xsl:if test="cac:Item/cac:SellersItemIdentification/cbc:ID and string-length(cac:Item/cac:SellersItemIdentification/cbc:ID) > 50">
      <svrl:failed-assert test="cac:Item/cac:SellersItemIdentification/cbc:ID" location="InvoiceLine/Item">
        <svrl:text>AR-25: Seller's item identification must not exceed 50 characters.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

  </xsl:template>

  <!-- Tax Subtotal validation -->
  <xsl:template match="cac:TaxSubtotal">
    
    <!-- Rule AR-31: Tax category validation -->
    <xsl:if test="cac:TaxCategory/cbc:ID">
      <xsl:variable name="taxCategoryId" select="cac:TaxCategory/cbc:ID"/>
      <xsl:if test="not($taxCategoryId = 'S' or $taxCategoryId = 'Z' or $taxCategoryId = 'E' or $taxCategoryId = 'AE' or $taxCategoryId = 'K' or $taxCategoryId = 'G' or $taxCategoryId = 'O' or $taxCategoryId = 'L' or $taxCategoryId = 'M')">
        <svrl:failed-assert test="cac:TaxCategory/cbc:ID" location="TaxSubtotal/TaxCategory">
          <svrl:text>AR-31: Tax category ID must be one of: S, Z, E, AE, K, G, O, L, M.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule AR-32: Tax percentage validation for standard rate -->
    <xsl:if test="cac:TaxCategory/cbc:ID = 'S' and not(cac:TaxCategory/cbc:Percent)">
      <svrl:failed-assert test="cac:TaxCategory/cbc:Percent" location="TaxSubtotal/TaxCategory">
        <svrl:text>AR-32: Standard rate tax category (S) must have a tax percentage.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-33: Zero rate validation -->
    <xsl:if test="cac:TaxCategory/cbc:ID = 'Z' and cac:TaxCategory/cbc:Percent and number(cac:TaxCategory/cbc:Percent) != 0">
      <svrl:failed-assert test="cac:TaxCategory/cbc:Percent" location="TaxSubtotal/TaxCategory">
        <svrl:text>AR-33: Zero rate tax category (Z) must have 0% tax percentage.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-34: Exempt validation -->
    <xsl:if test="cac:TaxCategory/cbc:ID = 'E' and cac:TaxCategory/cbc:Percent and number(cac:TaxCategory/cbc:Percent) != 0">
      <svrl:failed-assert test="cac:TaxCategory/cbc:Percent" location="TaxSubtotal/TaxCategory">
        <svrl:text>AR-34: Exempt tax category (E) must have 0% tax percentage.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule AR-35: Tax calculation validation -->
    <xsl:if test="cbc:TaxableAmount and cac:TaxCategory/cbc:Percent and cbc:TaxAmount">
      <xsl:variable name="taxableAmount" select="number(cbc:TaxableAmount)"/>
      <xsl:variable name="taxPercent" select="number(cac:TaxCategory/cbc:Percent)"/>
      <xsl:variable name="taxAmount" select="number(cbc:TaxAmount)"/>
      <xsl:variable name="expectedTaxAmount" select="round(($taxableAmount * $taxPercent div 100) * 100) div 100"/>
      
      <xsl:if test="abs($taxAmount - $expectedTaxAmount) > 0.01">
        <svrl:failed-assert test="cbc:TaxAmount" location="TaxSubtotal">
          <svrl:text>AR-35: Tax amount calculation error. Expected: <xsl:value-of select="$expectedTaxAmount"/>, Actual: <xsl:value-of select="$taxAmount"/>.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

  </xsl:template>

</xsl:stylesheet>
