<?xml version="1.0" encoding="UTF-8"?>
<xsl:stylesheet version="2.0" 
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
                xmlns:ubl="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:svrl="http://purl.oclc.org/dsdl/svrl">

  <xsl:output method="xml" version="1.0" encoding="UTF-8" indent="yes"/>

  <!-- Root template -->
  <xsl:template match="/">
    <svrl:schematron-output title="Peppol BIS 3.0 Business Rules Validation" 
                           schemaVersion="3.0">
      <xsl:apply-templates select="//ubl:Invoice"/>
    </svrl:schematron-output>
  </xsl:template>

  <!-- Peppol BIS 3.0 Invoice validation -->
  <xsl:template match="ubl:Invoice">
    
    <!-- Rule PEPPOL-EN16931-R001: Invoice must have customization identifier -->
    <xsl:if test="not(cbc:CustomizationID) or cbc:CustomizationID = ''">
      <svrl:failed-assert test="cbc:CustomizationID" location="Invoice">
        <svrl:text>PEPPOL-EN16931-R001: An Invoice must have a customization identifier (cbc:CustomizationID).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R002: Invoice must have profile identifier -->
    <xsl:if test="not(cbc:ProfileID) or cbc:ProfileID = ''">
      <svrl:failed-assert test="cbc:ProfileID" location="Invoice">
        <svrl:text>PEPPOL-EN16931-R002: An Invoice must have a profile identifier (cbc:ProfileID).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R003: Customization identifier must be Peppol BIS 3.0 -->
    <xsl:if test="cbc:CustomizationID and not(contains(cbc:CustomizationID, 'urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0'))">
      <svrl:failed-assert test="cbc:CustomizationID" location="Invoice">
        <svrl:text>PEPPOL-EN16931-R003: Customization identifier must be 'urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0'.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R004: Profile identifier must be Peppol BIS Billing -->
    <xsl:if test="cbc:ProfileID and not(cbc:ProfileID = 'urn:fdc:peppol.eu:2017:poacc:billing:01:1.0')">
      <svrl:failed-assert test="cbc:ProfileID" location="Invoice">
        <svrl:text>PEPPOL-EN16931-R004: Profile identifier must be 'urn:fdc:peppol.eu:2017:poacc:billing:01:1.0'.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R005: Supplier must have electronic address -->
    <xsl:if test="not(cac:AccountingSupplierParty/cac:Party/cbc:EndpointID)">
      <svrl:failed-assert test="cac:AccountingSupplierParty/cac:Party/cbc:EndpointID" location="Invoice/AccountingSupplierParty">
        <svrl:text>PEPPOL-EN16931-R005: Supplier must have an electronic address (cbc:EndpointID).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R006: Customer must have electronic address -->
    <xsl:if test="not(cac:AccountingCustomerParty/cac:Party/cbc:EndpointID)">
      <svrl:failed-assert test="cac:AccountingCustomerParty/cac:Party/cbc:EndpointID" location="Invoice/AccountingCustomerParty">
        <svrl:text>PEPPOL-EN16931-R006: Customer must have an electronic address (cbc:EndpointID).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R007: Supplier electronic address scheme must be valid -->
    <xsl:if test="cac:AccountingSupplierParty/cac:Party/cbc:EndpointID/@schemeID">
      <xsl:variable name="supplierScheme" select="cac:AccountingSupplierParty/cac:Party/cbc:EndpointID/@schemeID"/>
      <xsl:if test="not($supplierScheme = '0002' or $supplierScheme = '0007' or $supplierScheme = '0009' or $supplierScheme = '0037' or $supplierScheme = '0060' or $supplierScheme = '0088' or $supplierScheme = '0096' or $supplierScheme = '0130' or $supplierScheme = '0135' or $supplierScheme = '0142' or $supplierScheme = '0151' or $supplierScheme = '0183' or $supplierScheme = '0184' or $supplierScheme = '0190' or $supplierScheme = '0191' or $supplierScheme = '0192' or $supplierScheme = '0193' or $supplierScheme = '0195' or $supplierScheme = '0196' or $supplierScheme = '0198' or $supplierScheme = '0199' or $supplierScheme = '0200' or $supplierScheme = '0201' or $supplierScheme = '0202' or $supplierScheme = '0204' or $supplierScheme = '0208' or $supplierScheme = '0209' or $supplierScheme = '0210' or $supplierScheme = '0211' or $supplierScheme = '0212' or $supplierScheme = '0213' or $supplierScheme = '9901' or $supplierScheme = '9906' or $supplierScheme = '9907' or $supplierScheme = '9910' or $supplierScheme = '9913' or $supplierScheme = '9914' or $supplierScheme = '9915' or $supplierScheme = '9918' or $supplierScheme = '9919' or $supplierScheme = '9920' or $supplierScheme = '9922' or $supplierScheme = '9923' or $supplierScheme = '9924' or $supplierScheme = '9925' or $supplierScheme = '9926' or $supplierScheme = '9927' or $supplierScheme = '9928' or $supplierScheme = '9929' or $supplierScheme = '9930' or $supplierScheme = '9931' or $supplierScheme = '9932' or $supplierScheme = '9933' or $supplierScheme = '9934' or $supplierScheme = '9935' or $supplierScheme = '9936' or $supplierScheme = '9937' or $supplierScheme = '9938' or $supplierScheme = '9939' or $supplierScheme = '9940' or $supplierScheme = '9941' or $supplierScheme = '9942' or $supplierScheme = '9943' or $supplierScheme = '9944' or $supplierScheme = '9945' or $supplierScheme = '9946' or $supplierScheme = '9947' or $supplierScheme = '9948' or $supplierScheme = '9949' or $supplierScheme = '9950' or $supplierScheme = '9951' or $supplierScheme = '9952' or $supplierScheme = '9953' or $supplierScheme = '9955' or $supplierScheme = '9957')">
        <svrl:failed-assert test="cac:AccountingSupplierParty/cac:Party/cbc:EndpointID/@schemeID" location="Invoice/AccountingSupplierParty">
          <svrl:text>PEPPOL-EN16931-R007: Supplier electronic address scheme must be from the Peppol code list.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R008: Customer electronic address scheme must be valid -->
    <xsl:if test="cac:AccountingCustomerParty/cac:Party/cbc:EndpointID/@schemeID">
      <xsl:variable name="customerScheme" select="cac:AccountingCustomerParty/cac:Party/cbc:EndpointID/@schemeID"/>
      <xsl:if test="not($customerScheme = '0002' or $customerScheme = '0007' or $customerScheme = '0009' or $customerScheme = '0037' or $customerScheme = '0060' or $customerScheme = '0088' or $customerScheme = '0096' or $customerScheme = '0130' or $customerScheme = '0135' or $customerScheme = '0142' or $customerScheme = '0151' or $customerScheme = '0183' or $customerScheme = '0184' or $customerScheme = '0190' or $customerScheme = '0191' or $customerScheme = '0192' or $customerScheme = '0193' or $customerScheme = '0195' or $customerScheme = '0196' or $customerScheme = '0198' or $customerScheme = '0199' or $customerScheme = '0200' or $customerScheme = '0201' or $customerScheme = '0202' or $customerScheme = '0204' or $customerScheme = '0208' or $customerScheme = '0209' or $customerScheme = '0210' or $customerScheme = '0211' or $customerScheme = '0212' or $customerScheme = '0213' or $customerScheme = '9901' or $customerScheme = '9906' or $customerScheme = '9907' or $customerScheme = '9910' or $customerScheme = '9913' or $customerScheme = '9914' or $customerScheme = '9915' or $customerScheme = '9918' or $customerScheme = '9919' or $customerScheme = '9920' or $customerScheme = '9922' or $customerScheme = '9923' or $customerScheme = '9924' or $customerScheme = '9925' or $customerScheme = '9926' or $customerScheme = '9927' or $customerScheme = '9928' or $customerScheme = '9929' or $customerScheme = '9930' or $customerScheme = '9931' or $customerScheme = '9932' or $customerScheme = '9933' or $customerScheme = '9934' or $customerScheme = '9935' or $customerScheme = '9936' or $customerScheme = '9937' or $customerScheme = '9938' or $customerScheme = '9939' or $customerScheme = '9940' or $customerScheme = '9941' or $customerScheme = '9942' or $customerScheme = '9943' or $customerScheme = '9944' or $customerScheme = '9945' or $customerScheme = '9946' or $customerScheme = '9947' or $customerScheme = '9948' or $customerScheme = '9949' or $customerScheme = '9950' or $customerScheme = '9951' or $customerScheme = '9952' or $customerScheme = '9953' or $customerScheme = '9955' or $customerScheme = '9957')">
        <svrl:failed-assert test="cac:AccountingCustomerParty/cac:Party/cbc:EndpointID/@schemeID" location="Invoice/AccountingCustomerParty">
          <svrl:text>PEPPOL-EN16931-R008: Customer electronic address scheme must be from the Peppol code list.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R009: Supplier must have country code -->
    <xsl:if test="not(cac:AccountingSupplierParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode)">
      <svrl:failed-assert test="cac:AccountingSupplierParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode" location="Invoice/AccountingSupplierParty">
        <svrl:text>PEPPOL-EN16931-R009: Supplier must have a country code.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R010: Customer must have country code -->
    <xsl:if test="not(cac:AccountingCustomerParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode)">
      <svrl:failed-assert test="cac:AccountingCustomerParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode" location="Invoice/AccountingCustomerParty">
        <svrl:text>PEPPOL-EN16931-R010: Customer must have a country code.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R011: Country codes must be ISO 3166-1 alpha-2 -->
    <xsl:if test="cac:AccountingSupplierParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode">
      <xsl:variable name="supplierCountry" select="cac:AccountingSupplierParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode"/>
      <xsl:if test="not(matches($supplierCountry, '^[A-Z]{2}$'))">
        <svrl:failed-assert test="cac:AccountingSupplierParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode" location="Invoice/AccountingSupplierParty">
          <svrl:text>PEPPOL-EN16931-R011: Supplier country code must be ISO 3166-1 alpha-2 (2 uppercase letters).</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <xsl:if test="cac:AccountingCustomerParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode">
      <xsl:variable name="customerCountry" select="cac:AccountingCustomerParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode"/>
      <xsl:if test="not(matches($customerCountry, '^[A-Z]{2}$'))">
        <svrl:failed-assert test="cac:AccountingCustomerParty/cac:Party/cac:PostalAddress/cac:Country/cbc:IdentificationCode" location="Invoice/AccountingCustomerParty">
          <svrl:text>PEPPOL-EN16931-R011: Customer country code must be ISO 3166-1 alpha-2 (2 uppercase letters).</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R012: VAT identifier format validation -->
    <xsl:for-each select="cac:AccountingSupplierParty/cac:Party/cac:PartyTaxScheme[cac:TaxScheme/cbc:ID='VAT']/cbc:CompanyID">
      <xsl:if test="not(matches(., '^[A-Z]{2}[A-Z0-9]+$'))">
        <svrl:failed-assert test="." location="Invoice/AccountingSupplierParty/PartyTaxScheme">
          <svrl:text>PEPPOL-EN16931-R012: VAT identifier must start with country code followed by alphanumeric characters.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:for-each>

    <!-- Rule PEPPOL-EN16931-R013: Payment means code validation -->
    <xsl:for-each select="cac:PaymentMeans/cbc:PaymentMeansCode">
      <xsl:if test="not(. = '1' or . = '10' or . = '11' or . = '12' or . = '13' or . = '14' or . = '15' or . = '16' or . = '17' or . = '18' or . = '19' or . = '20' or . = '21' or . = '22' or . = '23' or . = '24' or . = '25' or . = '26' or . = '27' or . = '28' or . = '29' or . = '30' or . = '31' or . = '32' or . = '33' or . = '34' or . = '35' or . = '36' or . = '37' or . = '38' or . = '39' or . = '40' or . = '41' or . = '42' or . = '43' or . = '44' or . = '45' or . = '46' or . = '47' or . = '48' or . = '49' or . = '50' or . = '51' or . = '52' or . = '53' or . = '54' or . = '55' or . = '56' or . = '57' or . = '58' or . = '59' or . = '60' or . = '61' or . = '62' or . = '63' or . = '64' or . = '65' or . = '66' or . = '67' or . = '68' or . = '70' or . = '74' or . = '75' or . = '76' or . = '77' or . = '78' or . = '91' or . = '92' or . = '93' or . = '94' or . = '95' or . = '96' or . = '97' or . = 'ZZZ')">
        <svrl:failed-assert test="." location="Invoice/PaymentMeans">
          <svrl:text>PEPPOL-EN16931-R013: Payment means code must be from UN/ECE 4461 code list.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:for-each>

    <!-- Rule PEPPOL-EN16931-R014: Unit of measure validation -->
    <xsl:for-each select="cac:InvoiceLine/cbc:InvoicedQuantity/@unitCode">
      <xsl:if test="not(matches(., '^[A-Z0-9]{1,3}$'))">
        <svrl:failed-assert test="." location="InvoiceLine">
          <svrl:text>PEPPOL-EN16931-R014: Unit of measure must be from UN/ECE Recommendation 20.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:for-each>

    <!-- Rule PEPPOL-EN16931-R015: Allowance/charge reason code validation -->
    <xsl:for-each select="cac:AllowanceCharge/cbc:AllowanceChargeReasonCode | cac:InvoiceLine/cac:AllowanceCharge/cbc:AllowanceChargeReasonCode">
      <xsl:if test="not(matches(., '^[0-9]{1,3}$'))">
        <svrl:failed-assert test="." location="AllowanceCharge">
          <svrl:text>PEPPOL-EN16931-R015: Allowance/charge reason code must be from UNCL 5189 or UNCL 7161.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:for-each>

    <!-- Rule PEPPOL-EN16931-R016: Document reference type code validation -->
    <xsl:for-each select="cac:AdditionalDocumentReference/cbc:DocumentTypeCode">
      <xsl:if test="not(matches(., '^[0-9]{1,3}$'))">
        <svrl:failed-assert test="." location="AdditionalDocumentReference">
          <svrl:text>PEPPOL-EN16931-R016: Document reference type code must be from UNCL 1001.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:for-each>

    <!-- Process invoice lines for Peppol validation -->
    <xsl:apply-templates select="cac:InvoiceLine"/>

    <!-- Success message if all Peppol rules pass -->
    <xsl:if test="cbc:CustomizationID and cbc:ProfileID and cac:AccountingSupplierParty/cac:Party/cbc:EndpointID and cac:AccountingCustomerParty/cac:Party/cbc:EndpointID">
      <svrl:successful-report test="true()" location="Invoice">
        <svrl:text>Peppol BIS 3.0 validation passed for Invoice <xsl:value-of select="cbc:ID"/>.</svrl:text>
      </svrl:successful-report>
    </xsl:if>

  </xsl:template>

  <!-- Peppol Invoice Line validation -->
  <xsl:template match="cac:InvoiceLine">
    
    <!-- Rule PEPPOL-EN16931-R101: Invoice line must have item name -->
    <xsl:if test="not(cac:Item/cbc:Name) or cac:Item/cbc:Name = ''">
      <svrl:failed-assert test="cac:Item/cbc:Name" location="InvoiceLine/Item">
        <svrl:text>PEPPOL-EN16931-R101: Invoice line item must have a name (cbc:Name).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R102: Item classification scheme validation -->
    <xsl:for-each select="cac:Item/cac:CommodityClassification/cbc:ItemClassificationCode/@listID">
      <xsl:if test="not(. = 'STI' or . = 'SRV' or . = 'CPV' or . = 'CPC' or . = 'UNSPSC' or . = 'eCl@ss')">
        <svrl:failed-assert test="." location="InvoiceLine/Item/CommodityClassification">
          <svrl:text>PEPPOL-EN16931-R102: Item classification scheme must be STI, SRV, CPV, CPC, UNSPSC, or eCl@ss.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:for-each>

    <!-- Rule PEPPOL-EN16931-R103: Standard item identification scheme validation -->
    <xsl:if test="cac:Item/cac:StandardItemIdentification/cbc:ID/@schemeID">
      <xsl:variable name="scheme" select="cac:Item/cac:StandardItemIdentification/cbc:ID/@schemeID"/>
      <xsl:if test="not($scheme = '0002' or $scheme = '0003' or $scheme = '0004' or $scheme = '0010' or $scheme = '0088' or $scheme = '0096' or $scheme = '0130' or $scheme = '0160')">
        <svrl:failed-assert test="cac:Item/cac:StandardItemIdentification/cbc:ID/@schemeID" location="InvoiceLine/Item">
          <svrl:text>PEPPOL-EN16931-R103: Standard item identification scheme must be from ICD code list.</svrl:text>
        </svrl:failed-assert>
      </xsl:if>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R104: Tax category must be present for each line -->
    <xsl:if test="not(cac:Item/cac:ClassifiedTaxCategory/cbc:ID)">
      <svrl:failed-assert test="cac:Item/cac:ClassifiedTaxCategory/cbc:ID" location="InvoiceLine/Item">
        <svrl:text>PEPPOL-EN16931-R104: Invoice line item must have a tax category (cac:ClassifiedTaxCategory/cbc:ID).</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

    <!-- Rule PEPPOL-EN16931-R105: Tax scheme must be VAT -->
    <xsl:if test="cac:Item/cac:ClassifiedTaxCategory/cac:TaxScheme/cbc:ID and not(cac:Item/cac:ClassifiedTaxCategory/cac:TaxScheme/cbc:ID = 'VAT')">
      <svrl:failed-assert test="cac:Item/cac:ClassifiedTaxCategory/cac:TaxScheme/cbc:ID" location="InvoiceLine/Item/ClassifiedTaxCategory">
        <svrl:text>PEPPOL-EN16931-R105: Tax scheme must be 'VAT'.</svrl:text>
      </svrl:failed-assert>
    </xsl:if>

  </xsl:template>

</xsl:stylesheet>
