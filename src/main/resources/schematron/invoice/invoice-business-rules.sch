<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://purl.oclc.org/dsdl/schematron"
        xmlns:inv="urn:example:invoice"
        queryBinding="xslt2">

    <title>Invoice Business Rules Validation</title>
    <ns prefix="inv" uri="urn:example:invoice"/>

    <!-- Business Rule Patterns -->
    
    <pattern id="invoice-header-rules">
        <title>Invoice Header Business Rules</title>
        
        <rule context="inv:InvoiceHeader">
            <assert test="inv:InvoiceDate &lt;= current-date()"
                    id="BR-01">
                Invoice date must not be in the future.
            </assert>
            
            <assert test="inv:DueDate &gt;= inv:InvoiceDate"
                    id="BR-02">
                Due date must be on or after the invoice date.
            </assert>
            
            <assert test="string-length(inv:InvoiceNumber) &gt;= 3"
                    id="BR-03">
                Invoice number must be at least 3 characters long.
            </assert>
            
            <assert test="matches(inv:Currency, '^[A-Z]{3}$')"
                    id="BR-04">
                Currency must be a valid 3-letter ISO code.
            </assert>
        </rule>
        
        <rule context="inv:Supplier">
            <assert test="string-length(inv:Name) &gt; 0"
                    id="BR-05">
                Supplier name is required.
            </assert>
            
            <assert test="string-length(inv:Id) &gt; 0"
                    id="BR-06">
                Supplier ID is required.
            </assert>
        </rule>
        
        <rule context="inv:Customer">
            <assert test="string-length(inv:Name) &gt; 0"
                    id="BR-07">
                Customer name is required.
            </assert>
            
            <assert test="string-length(inv:Id) &gt; 0"
                    id="BR-08">
                Customer ID is required.
            </assert>
        </rule>
    </pattern>
    
    <pattern id="invoice-line-rules">
        <title>Invoice Line Business Rules</title>
        
        <rule context="inv:InvoiceLine">
            <assert test="inv:Quantity &gt; 0"
                    id="BR-09">
                Quantity must be greater than zero.
            </assert>
            
            <assert test="inv:UnitPrice &gt;= 0"
                    id="BR-10">
                Unit price must be zero or positive.
            </assert>
            
            <assert test="abs(inv:LineTotal - (inv:Quantity * inv:UnitPrice)) &lt; 0.01"
                    id="BR-11">
                Line total must equal quantity times unit price.
            </assert>
            
            <assert test="inv:TaxRate &gt;= 0 and inv:TaxRate &lt;= 1"
                    id="BR-12">
                Tax rate must be between 0 and 1 (0% to 100%).
            </assert>
            
            <assert test="abs(inv:TaxAmount - (inv:LineTotal * inv:TaxRate)) &lt; 0.01"
                    id="BR-13">
                Tax amount must equal line total times tax rate.
            </assert>
        </rule>
    </pattern>
    
    <pattern id="invoice-totals-rules">
        <title>Invoice Totals Business Rules</title>
        
        <rule context="inv:InvoiceTotals">
            <let name="calculatedSubTotal" value="sum(//inv:InvoiceLine/inv:LineTotal)"/>
            <let name="calculatedTotalTax" value="sum(//inv:InvoiceLine/inv:TaxAmount)"/>
            <let name="calculatedTotalAmount" value="$calculatedSubTotal + $calculatedTotalTax"/>
            
            <assert test="abs(inv:SubTotal - $calculatedSubTotal) &lt; 0.01"
                    id="BR-14">
                Subtotal must equal the sum of all line totals.
            </assert>
            
            <assert test="abs(inv:TotalTax - $calculatedTotalTax) &lt; 0.01"
                    id="BR-15">
                Total tax must equal the sum of all line tax amounts.
            </assert>
            
            <assert test="abs(inv:TotalAmount - $calculatedTotalAmount) &lt; 0.01"
                    id="BR-16">
                Total amount must equal subtotal plus total tax.
            </assert>
            
            <assert test="inv:AmountDue &gt;= 0"
                    id="BR-17">
                Amount due must be zero or positive.
            </assert>
        </rule>
    </pattern>
    
    <pattern id="data-integrity-rules">
        <title>Data Integrity Rules</title>
        
        <rule context="inv:Invoice">
            <assert test="count(inv:InvoiceLines/inv:InvoiceLine) &gt; 0"
                    id="BR-18">
                Invoice must contain at least one invoice line.
            </assert>
            
            <assert test="count(distinct-values(inv:InvoiceLines/inv:InvoiceLine/inv:LineNumber)) = count(inv:InvoiceLines/inv:InvoiceLine)"
                    id="BR-19">
                All invoice line numbers must be unique.
            </assert>
        </rule>
        
        <rule context="inv:Address">
            <assert test="string-length(inv:Country) = 2"
                    id="BR-20">
                Country code must be exactly 2 characters (ISO 3166-1 alpha-2).
            </assert>
            
            <assert test="matches(inv:PostalCode, '^[A-Z0-9\-\s]{3,10}$')"
                    id="BR-21">
                Postal code format is invalid.
            </assert>
        </rule>
    </pattern>
    
</schema>
