<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:example:invoice" version="1.0" id="INV-2024-001">
    <InvoiceHeader>
        <InvoiceNumber>INV-2024-001</InvoiceNumber>
        <InvoiceDate>2024-01-15</InvoiceDate>
        <DueDate>2024-02-15</DueDate>
        <Currency>USD</Currency>
        <Supplier>
            <Name>ACME Corporation</Name>
            <Id>ACME-001</Id>
            <TaxId>*********</TaxId>
            <Address>
                <Street>123 Business Street</Street>
                <City>Business City</City>
                <PostalCode>12345</PostalCode>
                <Country>US</Country>
            </Address>
            <Contact>
                <Email><EMAIL></Email>
                <Phone>******-0123</Phone>
            </Contact>
        </Supplier>
        <Customer>
            <Name>Customer Inc.</Name>
            <Id>CUST-001</Id>
            <TaxId>*********</TaxId>
            <Address>
                <Street>456 Customer Avenue</Street>
                <City>Customer City</City>
                <PostalCode>67890</PostalCode>
                <Country>US</Country>
            </Address>
            <Contact>
                <Email><EMAIL></Email>
                <Phone>******-0456</Phone>
            </Contact>
        </Customer>
        <PaymentTerms>Net 30</PaymentTerms>
    </InvoiceHeader>
    <InvoiceLines>
        <InvoiceLine>
            <LineNumber>1</LineNumber>
            <ItemId>ITEM-001</ItemId>
            <ItemName>Widget A</ItemName>
            <ItemDescription>High-quality widget for industrial use</ItemDescription>
            <Quantity>10</Quantity>
            <UnitPrice>25.00</UnitPrice>
            <LineTotal>250.00</LineTotal>
            <TaxRate>0.08</TaxRate>
            <TaxAmount>20.00</TaxAmount>
        </InvoiceLine>
        <InvoiceLine>
            <LineNumber>2</LineNumber>
            <ItemId>ITEM-002</ItemId>
            <ItemName>Widget B</ItemName>
            <ItemDescription>Premium widget with extended warranty</ItemDescription>
            <Quantity>5</Quantity>
            <UnitPrice>50.00</UnitPrice>
            <LineTotal>250.00</LineTotal>
            <TaxRate>0.08</TaxRate>
            <TaxAmount>20.00</TaxAmount>
        </InvoiceLine>
    </InvoiceLines>
    <InvoiceTotals>
        <SubTotal>500.00</SubTotal>
        <TotalTax>40.00</TotalTax>
        <TotalAmount>540.00</TotalAmount>
        <AmountDue>540.00</AmountDue>
    </InvoiceTotals>
</Invoice>
