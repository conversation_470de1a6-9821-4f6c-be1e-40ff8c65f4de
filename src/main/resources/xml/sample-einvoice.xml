<?xml version="1.0" encoding="UTF-8"?>
<Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
         xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
         xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">

  <!-- Basic Invoice Information -->
  <cbc:UBLVersionID>2.1</cbc:UBLVersionID>
  <cbc:CustomizationID>urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0</cbc:CustomizationID>
  <cbc:ProfileID>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</cbc:ProfileID>
  <cbc:ID>INV-2024-001</cbc:ID>
  <cbc:IssueDate>2024-01-15</cbc:IssueDate>
  <cbc:DueDate>2024-02-14</cbc:DueDate>
  <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
  <cbc:Note>Payment terms: 30 days net</cbc:Note>
  <cbc:DocumentCurrencyCode>EUR</cbc:DocumentCurrencyCode>
  <cbc:BuyerReference>PO-2024-001</cbc:BuyerReference>

  <!-- Invoice Period -->
  <cac:InvoicePeriod>
    <cbc:StartDate>2024-01-01</cbc:StartDate>
    <cbc:EndDate>2024-01-31</cbc:EndDate>
  </cac:InvoicePeriod>

  <!-- Order Reference -->
  <cac:OrderReference>
    <cbc:ID>ORDER-2024-001</cbc:ID>
  </cac:OrderReference>

  <!-- Supplier Party -->
  <cac:AccountingSupplierParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0088">*************</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0088">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Supplier Company Ltd</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Main Street 123</cbc:StreetName>
        <cbc:CityName>Stockholm</cbc:CityName>
        <cbc:PostalZone>11122</cbc:PostalZone>
        <cbc:CountrySubentity>Stockholm</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode>SE</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:RegistrationName>Supplier Company Ltd</cbc:RegistrationName>
        <cbc:CompanyID>SE123456789001</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Supplier Company Ltd</cbc:RegistrationName>
        <cbc:CompanyID>123456-7890</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>John Doe</cbc:Name>
        <cbc:Telephone>+46-8-123456</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingSupplierParty>

  <!-- Customer Party -->
  <cac:AccountingCustomerParty>
    <cac:Party>
      <cbc:EndpointID schemeID="0088">*************</cbc:EndpointID>
      <cac:PartyIdentification>
        <cbc:ID schemeID="0088">*************</cbc:ID>
      </cac:PartyIdentification>
      <cac:PartyName>
        <cbc:Name>Customer Company AB</cbc:Name>
      </cac:PartyName>
      <cac:PostalAddress>
        <cbc:StreetName>Customer Street 456</cbc:StreetName>
        <cbc:CityName>Gothenburg</cbc:CityName>
        <cbc:PostalZone>41234</cbc:PostalZone>
        <cbc:CountrySubentity>Västra Götaland</cbc:CountrySubentity>
        <cac:Country>
          <cbc:IdentificationCode>SE</cbc:IdentificationCode>
        </cac:Country>
      </cac:PostalAddress>
      <cac:PartyTaxScheme>
        <cbc:RegistrationName>Customer Company AB</cbc:RegistrationName>
        <cbc:CompanyID>SE987654321001</cbc:CompanyID>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:PartyTaxScheme>
      <cac:PartyLegalEntity>
        <cbc:RegistrationName>Customer Company AB</cbc:RegistrationName>
        <cbc:CompanyID>987654-3210</cbc:CompanyID>
      </cac:PartyLegalEntity>
      <cac:Contact>
        <cbc:Name>Jane Smith</cbc:Name>
        <cbc:Telephone>+46-31-654321</cbc:Telephone>
        <cbc:ElectronicMail><EMAIL></cbc:ElectronicMail>
      </cac:Contact>
    </cac:Party>
  </cac:AccountingCustomerParty>

  <!-- Payment Means -->
  <cac:PaymentMeans>
    <cbc:PaymentMeansCode>30</cbc:PaymentMeansCode>
    <cbc:PaymentMeansText>Credit transfer</cbc:PaymentMeansText>
    <cac:PayeeFinancialAccount>
      <cbc:ID>************************</cbc:ID>
      <cbc:Name>Supplier Company Ltd</cbc:Name>
      <cac:FinancialInstitutionBranch>
        <cbc:ID>SWEDSESS</cbc:ID>
      </cac:FinancialInstitutionBranch>
    </cac:PayeeFinancialAccount>
  </cac:PaymentMeans>

  <!-- Payment Terms -->
  <cac:PaymentTerms>
    <cbc:Note>Payment within 30 days, 2% discount if paid within 10 days</cbc:Note>
  </cac:PaymentTerms>

  <!-- Tax Total -->
  <cac:TaxTotal>
    <cbc:TaxAmount currencyID="EUR">250.00</cbc:TaxAmount>
    <cac:TaxSubtotal>
      <cbc:TaxableAmount currencyID="EUR">1000.00</cbc:TaxableAmount>
      <cbc:TaxAmount currencyID="EUR">250.00</cbc:TaxAmount>
      <cac:TaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>25.00</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:TaxCategory>
    </cac:TaxSubtotal>
  </cac:TaxTotal>

  <!-- Legal Monetary Total -->
  <cac:LegalMonetaryTotal>
    <cbc:LineExtensionAmount currencyID="EUR">1000.00</cbc:LineExtensionAmount>
    <cbc:TaxExclusiveAmount currencyID="EUR">1000.00</cbc:TaxExclusiveAmount>
    <cbc:TaxInclusiveAmount currencyID="EUR">1250.00</cbc:TaxInclusiveAmount>
    <cbc:PayableAmount currencyID="EUR">1250.00</cbc:PayableAmount>
  </cac:LegalMonetaryTotal>

  <!-- Invoice Lines -->
  <cac:InvoiceLine>
    <cbc:ID>1</cbc:ID>
    <cbc:Note>Professional services for January 2024</cbc:Note>
    <cbc:InvoicedQuantity unitCode="HUR">40</cbc:InvoicedQuantity>
    <cbc:LineExtensionAmount currencyID="EUR">1000.00</cbc:LineExtensionAmount>
    <cac:Item>
      <cbc:Description>Software development services</cbc:Description>
      <cbc:Name>Professional Services</cbc:Name>
      <cac:SellersItemIdentification>
        <cbc:ID>SRV-001</cbc:ID>
      </cac:SellersItemIdentification>
      <cac:CommodityClassification>
        <cbc:ItemClassificationCode listID="SRV">72.22.10</cbc:ItemClassificationCode>
      </cac:CommodityClassification>
      <cac:ClassifiedTaxCategory>
        <cbc:ID>S</cbc:ID>
        <cbc:Percent>25.00</cbc:Percent>
        <cac:TaxScheme>
          <cbc:ID>VAT</cbc:ID>
        </cac:TaxScheme>
      </cac:ClassifiedTaxCategory>
    </cac:Item>
    <cac:Price>
      <cbc:PriceAmount currencyID="EUR">25.00</cbc:PriceAmount>
      <cbc:BaseQuantity unitCode="HUR">1</cbc:BaseQuantity>
    </cac:Price>
  </cac:InvoiceLine>

</Invoice>
