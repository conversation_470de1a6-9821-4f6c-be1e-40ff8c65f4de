#!/usr/bin/env python3
"""
Simple XSD validation script to test the basic-example.xsd schema
"""

import xml.etree.ElementTree as ET
from lxml import etree
import sys
import os

def validate_xml_with_xsd(xml_file, xsd_file):
    """Validate XML file against XSD schema"""
    try:
        # Parse XSD schema
        with open(xsd_file, 'r', encoding='utf-8') as schema_file:
            schema_root = etree.XML(schema_file.read())
            schema = etree.XMLSchema(schema_root)
            xmlparser = etree.XMLParser(schema=schema)
        
        # Parse and validate XML
        with open(xml_file, 'r', encoding='utf-8') as xml_content:
            etree.parse(xml_content, xmlparser)
        
        print(f"✅ {xml_file} is VALID against {xsd_file}")
        return True
        
    except etree.XMLSyntaxError as e:
        print(f"❌ {xml_file} is INVALID against {xsd_file}")
        print(f"   Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error processing {xml_file}: {e}")
        return False

def main():
    """Main validation function"""
    print("XSD Validation Test")
    print("=" * 50)
    
    # Define file paths
    xsd_file = "src/main/resources/schemas/basic-example.xsd"
    test_files = [
        "src/test/resources/xml/valid-basic-document.xml",
        "src/test/resources/xml/minimal-basic-document.xml",
        "src/test/resources/xml/invalid-basic-document.xml"
    ]
    
    # Check if XSD file exists
    if not os.path.exists(xsd_file):
        print(f"❌ XSD file not found: {xsd_file}")
        return
    
    print(f"Using XSD schema: {xsd_file}")
    print()
    
    # Validate each test file
    results = []
    for xml_file in test_files:
        if os.path.exists(xml_file):
            result = validate_xml_with_xsd(xml_file, xsd_file)
            results.append((xml_file, result))
        else:
            print(f"⚠️  Test file not found: {xml_file}")
            results.append((xml_file, None))
    
    # Summary
    print()
    print("Summary:")
    print("-" * 30)
    for xml_file, result in results:
        filename = os.path.basename(xml_file)
        if result is True:
            print(f"✅ {filename}: VALID")
        elif result is False:
            print(f"❌ {filename}: INVALID")
        else:
            print(f"⚠️  {filename}: NOT FOUND")

if __name__ == "__main__":
    try:
        main()
    except ImportError:
        print("❌ This script requires the 'lxml' library.")
        print("   Install it with: pip install lxml")
        sys.exit(1)
