import javax.xml.XMLConstants;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import java.io.File;
import java.io.StringReader;

/**
 * Simple standalone XSD validator for testing the basic-example.xsd schema
 * Compile and run with: javac SimpleXSDValidator.java && java SimpleXSDValidator
 */
public class SimpleXSDValidator {
    
    public static void main(String[] args) {
        System.out.println("XSD Validation Test");
        System.out.println("=".repeat(50));
        
        String xsdPath = "src/main/resources/schemas/basic-example.xsd";
        
        // Test XML documents
        String[] testXmlFiles = {
            "src/test/resources/xml/valid-basic-document.xml",
            "src/test/resources/xml/minimal-basic-document.xml",
            "src/test/resources/xml/invalid-basic-document.xml"
        };
        
        // Inline test XML for quick testing
        String validXml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <document xmlns="urn:example:basic" version="1.0">
              <header>
                <title>Test Document</title>
                <author>Test Author</author>
                <date>2024-06-30</date>
              </header>
              <body>
                <section id="test1">
                  <title>Test Section</title>
                  <content>This is test content.</content>
                </section>
              </body>
            </document>
            """;
        
        String invalidXml = """
            <?xml version="1.0" encoding="UTF-8"?>
            <document xmlns="urn:example:basic">
              <!-- Missing required version attribute -->
              <header>
                <title>Invalid Document</title>
              </header>
              <body>
                <section>
                  <!-- Missing required id attribute -->
                  <title>Invalid Section</title>
                  <content>Invalid content.</content>
                </section>
              </body>
            </document>
            """;
        
        try {
            // Load XSD schema
            SchemaFactory factory = SchemaFactory.newInstance(XMLConstants.W3C_XML_SCHEMA_NS_URI);
            Schema schema = factory.newSchema(new File(xsdPath));
            Validator validator = schema.newValidator();
            
            System.out.println("Using XSD schema: " + xsdPath);
            System.out.println();
            
            // Test valid XML
            System.out.println("Testing VALID XML:");
            validateXmlString(validator, validXml, "Valid XML");
            
            System.out.println();
            
            // Test invalid XML
            System.out.println("Testing INVALID XML:");
            validateXmlString(validator, invalidXml, "Invalid XML");
            
            System.out.println();
            
            // Test XML files if they exist
            System.out.println("Testing XML files:");
            for (String xmlFile : testXmlFiles) {
                File file = new File(xmlFile);
                if (file.exists()) {
                    validateXmlFile(validator, xmlFile);
                } else {
                    System.out.println("⚠️  File not found: " + xmlFile);
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void validateXmlString(Validator validator, String xmlContent, String description) {
        try {
            validator.validate(new StreamSource(new StringReader(xmlContent)));
            System.out.println("✅ " + description + ": VALID");
        } catch (Exception e) {
            System.out.println("❌ " + description + ": INVALID");
            System.out.println("   Error: " + e.getMessage());
        }
    }
    
    private static void validateXmlFile(Validator validator, String xmlFilePath) {
        try {
            validator.validate(new StreamSource(new File(xmlFilePath)));
            System.out.println("✅ " + new File(xmlFilePath).getName() + ": VALID");
        } catch (Exception e) {
            System.out.println("❌ " + new File(xmlFilePath).getName() + ": INVALID");
            System.out.println("   Error: " + e.getMessage());
        }
    }
}
