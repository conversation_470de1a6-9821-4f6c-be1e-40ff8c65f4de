# Custom PHIVE Validation System

This is a comprehensive, extensible PHIVE validation implementation that provides XML validation with XSD schemas and Schematron business rules. The system is designed to be easily extended and customized for your specific validation requirements.

## Features

- **Multi-layer Validation**: XSD schema validation + Schematron business rules
- **Extensible Architecture**: Abstract base classes for easy customization
- **REST API**: Complete REST endpoints for validation operations
- **Performance Optimized**: Caching and efficient validation execution
- **Detailed Reporting**: Comprehensive error reporting with location information
- **Business Rules**: Support for complex business logic validation
- **Batch Processing**: Validate multiple documents in a single request

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    REST Controller Layer                    │
│  CustomValidationController - API endpoints                │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     Service Layer                          │
│  CustomValidationService - Business logic                  │
│  AbstractValidationService - Base functionality            │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    PHIVE Core Layer                        │
│  ValidationExecutorSetRegistry - Rule management           │
│  ValidationExecutorXSD - XSD validation                    │
│  ValidationExecutorSchematron - Business rules             │
└─────────────────────────────────────────────────────────────┘
```

## Quick Start

### 1. Basic Usage

```java
@Autowired
private CustomValidationService validationService;

// Simple validation
ValidationResult result = validationService.validateXml(xmlContent, "com.example.validation:invoice:1.0");

// Enhanced validation with options
ValidationRequest request = ValidationRequest.builder()
    .xmlContent(xmlContent)
    .validationSetId("com.example.validation:invoice:1.0")
    .includeWarnings(true)
    .includeMetrics(true)
    .build();

ValidationResult result = validationService.validateDocument(request);
```

### 2. REST API Usage

```bash
# Validate a document
curl -X POST http://localhost:8080/api/v1/custom-validation/validate \
  -H "Content-Type: application/json" \
  -d '{
    "xmlContent": "<Invoice>...</Invoice>",
    "validationSetId": "com.example.validation:invoice:1.0",
    "includeWarnings": true
  }'

# Get available validation sets
curl http://localhost:8080/api/v1/custom-validation/validation-sets

# Health check
curl http://localhost:8080/api/v1/custom-validation/health
```

## Extending the System

### 1. Creating Custom Validation Service

```java
@Service
public class MyCustomValidationService extends AbstractValidationService {

    @Override
    public void registerValidationRules() {
        // Register your custom validation sets
        registerMyCustomValidation();
    }

    private void registerMyCustomValidation() {
        // Create XSD validator
        ValidationExecutorXSD xsdValidator = createXSDValidator("schemas/my-schema.xsd");
        
        // Create Schematron validator
        ValidationExecutorSchematron schematronValidator = 
            createSchematronValidator("schematron/my-rules.xslt");
        
        // Create validation set
        ValidationExecutorSet<IValidationSourceXML> executorSet = 
            createValidationExecutorSet(
                "com.mycompany", "my-document", "1.0",
                "My Custom Document Validation",
                xsdValidator, schematronValidator
            );
        
        // Register the validation set
        registerValidationExecutorSet(executorSet);
    }

    @Override
    protected void preValidationHook(IValidationExecutorSet<IValidationSourceXML> executorSet, 
                                   IValidationSourceXML validationSource) {
        // Add custom pre-validation logic
        log.info("Custom pre-validation for: {}", executorSet.getID());
    }

    @Override
    protected void postValidationHook(IValidationExecutorSet<IValidationSourceXML> executorSet, 
                                    IValidationSourceXML validationSource, 
                                    ValidationResultList resultList) {
        // Add custom post-validation logic
        log.info("Custom post-validation completed with {} errors", 
                resultList.getErrorCount());
    }
}
```

### 2. Adding Custom Validation Rules

#### XSD Schema Example
```xml
<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="urn:mycompany:mydocument"
           elementFormDefault="qualified">
    
    <xs:element name="MyDocument" type="tns:MyDocumentType"/>
    
    <xs:complexType name="MyDocumentType">
        <xs:sequence>
            <xs:element name="Header" type="tns:HeaderType"/>
            <xs:element name="Body" type="tns:BodyType"/>
        </xs:sequence>
    </xs:complexType>
    
    <!-- Define your schema structure -->
</xs:schema>
```

#### Schematron Business Rules Example
```xml
<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://purl.oclc.org/dsdl/schematron"
        xmlns:my="urn:mycompany:mydocument">

    <ns prefix="my" uri="urn:mycompany:mydocument"/>

    <pattern id="business-rules">
        <rule context="my:MyDocument">
            <assert test="my:Header/my:DocumentDate &lt;= current-date()"
                    id="BR-001">
                Document date must not be in the future.
            </assert>
        </rule>
    </pattern>
</schema>
```

### 3. Custom REST Controller

```java
@RestController
@RequestMapping("/api/v1/my-validation")
public class MyValidationController {

    @Autowired
    private MyCustomValidationService validationService;

    @PostMapping("/validate-special")
    public ResponseEntity<ValidationResult> validateSpecialDocument(
            @RequestBody MySpecialValidationRequest request) {
        
        // Add custom validation logic
        ValidationResult result = validationService.validateSpecialDocument(request);
        
        return ResponseEntity.ok(result);
    }
}
```

## Configuration

### Application Properties
```properties
# PHIVE Validation Configuration
validation.cache.enabled=true
validation.cache.size=100
validation.performance.metrics.enabled=true
validation.detailed.errors.enabled=true

# Logging
logging.level.com.example.demo.validation=DEBUG
logging.level.com.helger.phive=INFO
```

### Custom Configuration
```java
@Configuration
public class MyValidationConfiguration {

    @Bean
    @Primary
    public ValidationExecutorSetRegistry<IValidationSourceXML> customValidationRegistry() {
        ValidationExecutorSetRegistry<IValidationSourceXML> registry = 
            new ValidationExecutorSetRegistry<>();
        
        // Add custom initialization logic
        return registry;
    }
}
```

## Available Validation Sets

The system comes with pre-configured validation sets:

1. **Basic XML Validation** (`com.example.validation:basic-xml:1.0`)
   - XSD schema validation only
   - For simple document structure validation

2. **Invoice Validation** (`com.example.validation:invoice:1.0`)
   - XSD schema validation
   - Business rules validation
   - Advanced calculation validation

3. **Purchase Order Validation** (`com.example.validation:purchase-order:1.0`)
   - XSD schema validation
   - Business rules with namespace support

4. **Custom Document Validation** (`com.example.validation:custom-document:1.0`)
   - Extensible validation framework

## API Endpoints

### Validation Endpoints
- `POST /api/v1/custom-validation/validate` - Main validation endpoint
- `POST /api/v1/custom-validation/validate-simple` - Simple validation
- `POST /api/v1/custom-validation/validate-batch` - Batch validation

### Management Endpoints
- `GET /api/v1/custom-validation/validation-sets` - List available validation sets
- `GET /api/v1/custom-validation/validation-sets/{id}` - Get specific validation set info
- `GET /api/v1/custom-validation/health` - Health check
- `GET /api/v1/custom-validation/stats` - Validation statistics

### Admin Endpoints
- `POST /api/v1/custom-validation/admin/clear-cache` - Clear validation cache
- `POST /api/v1/custom-validation/admin/reload-rules` - Reload validation rules

## Testing

### Sample Test
```java
@SpringBootTest
class CustomValidationServiceTest {

    @Autowired
    private CustomValidationService validationService;

    @Test
    void testInvoiceValidation() {
        String xmlContent = loadTestXml("sample-invoice.xml");
        
        ValidationResult result = validationService.validateXml(
            xmlContent, "com.example.validation:invoice:1.0");
        
        assertTrue(result.isSuccess());
        assertNotNull(result.getMetrics());
    }
}
```

## Performance Considerations

- **Caching**: Validation executor sets are cached for performance
- **Thread Safety**: All validation operations are thread-safe
- **Memory Management**: Large XML documents are processed efficiently
- **Batch Processing**: Use batch endpoints for multiple documents

## Troubleshooting

### Common Issues

1. **Schema Not Found**: Ensure XSD files are in the correct classpath location
2. **Schematron Compilation**: Check XSLT transformation files are valid
3. **Namespace Issues**: Verify namespace mappings in Schematron rules
4. **Performance**: Use caching and consider batch processing for multiple validations

### Debug Logging
```properties
logging.level.com.example.demo.validation=DEBUG
logging.level.com.helger.phive=DEBUG
```

## Future Extensions

The system is designed to support:
- Dynamic rule loading from database
- Custom validation result processors
- Integration with external validation services
- Real-time validation rule updates
- Advanced metrics and monitoring
- Custom error message templates

## Dependencies

Key PHIVE dependencies:
- `phive-api`: Core validation interfaces
- `phive-xml`: XML validation implementation
- `ph-schematron-xslt`: Schematron validation
- `ph-commons`: Utility libraries
